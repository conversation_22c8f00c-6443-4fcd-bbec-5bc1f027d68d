<?php
namespace app\model\pharmacy;

use app\model\BaseModel;
use think\facade\Db;

/**
 * 套餐生产记录模型
 * Class PharmacyProductionLog
 * @package app\model\pharmacy
 */
class PharmacyProductionLog extends BaseModel
{
    protected $table = 'pharmacy_production_log';
    
    /**
     * 获取生产记录列表
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getProductionList($condition = [], $page = 1, $page_size = 20)
    {
        // 使用原生查询方式实现分页
        $offset = ($page - 1) * $page_size;

        // 获取总数
        $count = \think\facade\Db::name($this->table)->where($condition)->count();

        // 获取列表数据
        $list = \think\facade\Db::name($this->table)
            ->where($condition)
            ->order('create_time desc')
            ->limit($offset, $page_size)
            ->select()
            ->toArray();

        if (!empty($list)) {
            foreach ($list as &$item) {
                $item['create_time_format'] = date('Y-m-d H:i:s', strtotime($item['create_time']));
                $item['consumed_details_array'] = json_decode($item['consumed_details'], true);
                $item['produced_loose_details_array'] = json_decode($item['produced_loose_details'], true);
                $item['status_name'] = $item['status'] == 1 ? '成功' : '失败';

                // 计算生产统计
                $item['total_bottles_consumed'] = $this->calculateTotalBottles($item['consumed_details_array']);
                $item['total_loose_produced'] = $this->calculateTotalLoose($item['produced_loose_details_array']);
            }
        }

        $result = [
            'count' => $count,
            'list' => $list,
            'page_count' => ceil($count / $page_size)
        ];

        return $this->success($result);
    }
    
    /**
     * 获取生产记录详情
     * @param int $production_id 生产记录ID
     * @return array
     */
    public function getProductionDetail($production_id)
    {
        $production_info = Db::name($this->table)->where([['id', '=', $production_id]])->find();

        if (empty($production_info)) {
            return $this->error('', '生产记录不存在');
        }

        // 解析JSON数据
        $production_info['consumed_details_array'] = json_decode($production_info['consumed_details'], true) ?: [];
        $production_info['produced_loose_details_array'] = json_decode($production_info['produced_loose_details'], true) ?: [];
        $production_info['create_time_format'] = date('Y-m-d H:i:s', strtotime($production_info['create_time']));
        $production_info['status_name'] = $production_info['status'] == 1 ? '成功' : '失败';

        // 计算统计信息
        $production_info['total_bottles_consumed'] = $this->calculateTotalBottles($production_info['consumed_details_array']);
        $production_info['total_loose_produced'] = $this->calculateTotalLoose($production_info['produced_loose_details_array']);

        // 格式化消耗明细文本
        $production_info['consumed_details_text'] = $this->formatConsumedDetails($production_info['consumed_details_array']);

        // 获取相关的库存操作日志
        $stock_logs = Db::name('pharmacy_stock_log')->where([
            ['related_id', '=', $production_id],
            ['operation_type', '=', 2]
        ])->select()->toArray();

        $production_info['related_stock_logs'] = $stock_logs;

        return $this->success($production_info);
    }
    
    /**
     * 获取生产统计信息
     * @param array $condition 查询条件
     * @return array
     */
    public function getProductionStats($condition = [])
    {
        // 今日生产统计
        $today_condition = array_merge($condition, [
            ['create_time', '>=', date('Y-m-d 00:00:00')],
            ['create_time', '<=', date('Y-m-d 23:59:59')],
            ['status', '=', 1]
        ]);
        
        $today_stats = model($this->table)->field('COUNT(*) as count, SUM(production_quantity) as total_quantity, SUM(total_cost) as total_cost')
            ->where($today_condition)
            ->find();
        
        // 本月生产统计
        $month_condition = array_merge($condition, [
            ['create_time', '>=', date('Y-m-01 00:00:00')],
            ['create_time', '<=', date('Y-m-t 23:59:59')],
            ['status', '=', 1]
        ]);
        
        $month_stats = model($this->table)->field('COUNT(*) as count, SUM(production_quantity) as total_quantity, SUM(total_cost) as total_cost')
            ->where($month_condition)
            ->find();

        // 按模板统计
        $template_stats = model($this->table)->field('template_id, template_name, COUNT(*) as count, SUM(production_quantity) as total_quantity')
            ->where(array_merge($condition, [['status', '=', 1]]))
            ->group('template_id')
            ->order('count desc')
            ->limit(10)
            ->select();

        // 按操作员统计
        $operator_stats = model($this->table)->field('operator_id, operator_name, COUNT(*) as count, SUM(production_quantity) as total_quantity')
            ->where(array_merge($condition, [['status', '=', 1]]))
            ->group('operator_id')
            ->order('count desc')
            ->limit(10)
            ->select();
        
        return $this->success([
            'today_stats' => [
                'count' => $today_stats['count'] ?? 0,
                'total_quantity' => $today_stats['total_quantity'] ?? 0,
                'total_cost' => $today_stats['total_cost'] ?? 0
            ],
            'month_stats' => [
                'count' => $month_stats['count'] ?? 0,
                'total_quantity' => $month_stats['total_quantity'] ?? 0,
                'total_cost' => $month_stats['total_cost'] ?? 0
            ],
            'template_stats' => $template_stats,
            'operator_stats' => $operator_stats
        ]);
    }
    
    /**
     * 获取模板生产历史
     * @param int $template_id 模板ID
     * @param int $limit 限制数量
     * @return array
     */
    public function getTemplateHistory($template_id, $limit = 10)
    {
        $condition = [
            ['template_id', '=', $template_id],
            ['status', '=', 1]
        ];
        
        $list = model($this->table)->where($condition)
            ->field('*')
            ->order('create_time desc')
            ->limit($limit)
            ->select();
        
        foreach ($list as &$item) {
            $item['create_time_format'] = date('Y-m-d H:i:s', strtotime($item['create_time']));
            $item['consumed_details_array'] = json_decode($item['consumed_details'], true);
            $item['total_bottles_consumed'] = $this->calculateTotalBottles($item['consumed_details_array']);
        }
        
        return $this->success($list);
    }
    
    /**
     * 获取操作员生产记录
     * @param int $operator_id 操作员ID
     * @param array $date_range 日期范围
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getOperatorProductions($operator_id, $date_range = [], $page = 1, $page_size = 20)
    {
        $condition = [['operator_id', '=', $operator_id]];
        
        if (!empty($date_range) && count($date_range) == 2) {
            $condition[] = ['create_time', '>=', $date_range[0]];
            $condition[] = ['create_time', '<=', $date_range[1]];
        }
        
        return $this->getProductionList($condition, $page, $page_size);
    }
    
    /**
     * 导出生产记录
     * @param array $condition 查询条件
     * @param string $format 导出格式
     * @return array
     */
    public function exportProductions($condition = [], $format = 'csv')
    {
        $list = model($this->table)->where($condition)
            ->field('*')
            ->order('create_time desc')
            ->select();
        
        $export_data = [];
        $export_data[] = [
            '生产ID', '模板名称', '生产数量', '操作员', '总成本', 
            '状态', '生产时间', '消耗详情', '产生散药详情'
        ];
        
        foreach ($list as $item) {
            $consumed_details = json_decode($item['consumed_details'], true);
            $produced_details = json_decode($item['produced_loose_details'], true);
            
            $consumed_summary = $this->formatConsumedSummary($consumed_details);
            $produced_summary = $this->formatProducedSummary($produced_details);
            
            $export_data[] = [
                $item['id'],
                $item['template_name'],
                $item['production_quantity'],
                $item['operator_name'],
                $item['total_cost'],
                $item['status'] == 1 ? '成功' : '失败',
                $item['create_time'],
                $consumed_summary,
                $produced_summary
            ];
        }
        
        return $this->success([
            'data' => $export_data,
            'filename' => 'pharmacy_production_log_' . date('YmdHis') . '.' . $format,
            'total_count' => count($list) - 1
        ]);
    }
    
    /**
     * 计算总消耗瓶数
     * @param array $consumed_details 消耗详情
     * @return int
     */
    private function calculateTotalBottles($consumed_details)
    {
        $total = 0;
        if (!empty($consumed_details['items'])) {
            foreach ($consumed_details['items'] as $item) {
                $total += $item['bottles_consumed'] ?? 0;
            }
        }
        return $total;
    }
    
    /**
     * 计算总产生散药数
     * @param array $produced_details 产生详情
     * @return int
     */
    private function calculateTotalLoose($produced_details)
    {
        $total = 0;
        if (!empty($produced_details['items'])) {
            foreach ($produced_details['items'] as $item) {
                $total += $item['loose_grains_added'] ?? 0;
            }
        }
        return $total;
    }
    
    /**
     * 格式化消耗摘要
     * @param array $consumed_details 消耗详情
     * @return string
     */
    private function formatConsumedSummary($consumed_details)
    {
        $summary = [];
        if (!empty($consumed_details['items'])) {
            foreach ($consumed_details['items'] as $item) {
                $summary[] = $item['sku_name'] . ':' . $item['bottles_consumed'] . '瓶';
            }
        }
        return implode('; ', $summary);
    }
    
    /**
     * 格式化产生摘要
     * @param array $produced_details 产生详情
     * @return string
     */
    private function formatProducedSummary($produced_details)
    {
        $summary = [];
        if (!empty($produced_details['items'])) {
            foreach ($produced_details['items'] as $item) {
                if ($item['loose_grains_added'] > 0) {
                    $summary[] = $item['sku_name'] . ':+' . $item['loose_grains_added'] . '颗';
                }
            }
        }
        return implode('; ', $summary);
    }
    
    /**
     * 添加生产记录
     * @param array $data 生产数据
     * @return int|false
     */
    public function addProduction($data)
    {
        // 确保必要字段存在
        $required_fields = ['site_id', 'template_id', 'template_name', 'production_quantity', 'operator_id', 'operator_name'];
        
        foreach ($required_fields as $field) {
            if (!isset($data[$field])) {
                return false;
            }
        }
        
        // 设置默认值
        $data['create_time'] = $data['create_time'] ?? date('Y-m-d H:i:s');
        $data['consumed_details'] = $data['consumed_details'] ?? '{}';
        $data['produced_loose_details'] = $data['produced_loose_details'] ?? '{}';
        $data['total_cost'] = $data['total_cost'] ?? 0;
        $data['status'] = $data['status'] ?? 1;
        $data['error_message'] = $data['error_message'] ?? '';
        
        return model($this->table)->add($data);
    }

    /**
     * 格式化消耗明细为HTML文本
     * @param array $consumed_details 消耗明细数组
     * @return string
     */
    private function formatConsumedDetails($consumed_details)
    {
        if (empty($consumed_details) || !isset($consumed_details['items'])) {
            return '<p style="color: #999; text-align: center; padding: 20px;">暂无消耗明细</p>';
        }

        $html = '<table style="width: 100%; border-collapse: collapse; margin-top: 10px;">';
        $html .= '<thead>';
        $html .= '<tr style="background: #f8f9fa;">';
        $html .= '<th style="padding: 8px; border: 1px solid #ddd; text-align: left;">商品名称</th>';
        $html .= '<th style="padding: 8px; border: 1px solid #ddd; text-align: center;">消耗瓶数</th>';
        $html .= '<th style="padding: 8px; border: 1px solid #ddd; text-align: center;">消耗颗数</th>';
        $html .= '<th style="padding: 8px; border: 1px solid #ddd; text-align: right;">单价</th>';
        $html .= '<th style="padding: 8px; border: 1px solid #ddd; text-align: right;">小计</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';

        foreach ($consumed_details['items'] as $item) {
            $html .= '<tr>';
            $html .= '<td style="padding: 8px; border: 1px solid #ddd;">' . htmlspecialchars($item['sku_name'] ?? '未知商品') . '</td>';
            $html .= '<td style="padding: 8px; border: 1px solid #ddd; text-align: center;">' . intval($item['bottles_consumed'] ?? 0) . '</td>';
            $html .= '<td style="padding: 8px; border: 1px solid #ddd; text-align: center;">' . intval($item['loose_consumed'] ?? 0) . '</td>';
            $html .= '<td style="padding: 8px; border: 1px solid #ddd; text-align: right;">¥' . number_format(floatval($item['cost_price'] ?? 0), 2) . '</td>';
            $html .= '<td style="padding: 8px; border: 1px solid #ddd; text-align: right;">¥' . number_format(floatval($item['total_cost'] ?? 0), 2) . '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';

        return $html;
    }

    /**
     * 获取今日生产记录（简化版本）
     * @param array $condition 查询条件
     * @return array
     */
    public function getTodayRecords($condition = [])
    {
        try {
            $list = \think\facade\Db::name($this->table)
                ->where($condition)
                ->field('id,template_name,production_quantity,final_quantity,quantity_confirmed,operator_name,create_time')
                ->order('create_time desc')
                ->limit(10)  // 限制显示最近10条记录
                ->select()
                ->toArray();

            // 格式化时间
            foreach ($list as &$item) {
                $item['create_time_format'] = date('H:i:s', strtotime($item['create_time']));
                // 只有已确认的记录才使用final_quantity，未确认的保持为null
                if ($item['quantity_confirmed'] != 1) {
                    $item['final_quantity'] = null;
                }
            }

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->error('', '获取今日记录失败：' . $e->getMessage());
        }
    }

    /**
     * 确认最终生产套数
     * @param int $production_id 生产记录ID
     * @param int $final_quantity 最终套数
     * @param array $operator_info 操作员信息
     * @return array
     */
    public function confirmFinalQuantity($production_id, $final_quantity, $operator_info)
    {
        try {
            // 1. 检查生产记录是否存在
            $production_info = \think\facade\Db::name($this->table)
                ->where('id', $production_id)
                ->find();

            if (empty($production_info)) {
                return $this->error('', '生产记录不存在');
            }

            // 2. 检查字段是否存在，如果不存在则只更新基本信息
            $update_data = [];

            // 检查final_quantity字段是否存在
            if ($this->checkFieldExists('final_quantity')) {
                $update_data['final_quantity'] = $final_quantity;
            }

            // 检查quantity_confirmed字段是否存在
            if ($this->checkFieldExists('quantity_confirmed')) {
                // 检查是否已经确认过
                if (isset($production_info['quantity_confirmed']) && $production_info['quantity_confirmed'] == 1) {
                    return $this->error('', '该生产记录已经确认过最终套数');
                }
                $update_data['quantity_confirmed'] = 1;
            }

            // 检查quantity_confirm_time字段是否存在
            if ($this->checkFieldExists('quantity_confirm_time')) {
                $update_data['quantity_confirm_time'] = date('Y-m-d H:i:s');
            }

            // 如果没有可更新的字段，返回错误
            if (empty($update_data)) {
                return $this->error('', '数据库表结构需要更新，请执行update.sql中的字段添加语句。缺少字段：final_quantity, quantity_confirmed, quantity_confirm_time');
            }

            // 记录调试信息
            $debug_info = [
                'production_id' => $production_id,
                'update_data' => $update_data,
                'table' => 'ns_' . $this->table
            ];

            // 使用直接SQL执行更新（因为ThinkPHP的update方法在某些情况下可能失败）
            $sql_parts = [];
            foreach ($update_data as $field => $value) {
                if (is_null($value)) {
                    $sql_parts[] = "`{$field}` = NULL";
                } elseif (is_string($value)) {
                    // 对字符串进行转义
                    $escaped_value = addslashes($value);
                    $sql_parts[] = "`{$field}` = '{$escaped_value}'";
                } else {
                    $sql_parts[] = "`{$field}` = {$value}";
                }
            }

            $update_sql = "UPDATE `ns_pharmacy_production_log` SET " . implode(', ', $sql_parts) . " WHERE `id` = " . intval($production_id);
            $result = \think\facade\Db::execute($update_sql);



            if ($result !== false) {
                // 4. 创建生产入库记录（新增功能）
                $stock_service = new \app\model\pharmacy\PharmacyStockService();
                $stock_result = $stock_service->createProductionStockRecords($production_id, $final_quantity, $operator_info);

                // 5. 记录操作日志（可选）
                $log_data = [
                    'site_id' => $operator_info['site_id'],
                    'sku_id' => 0, // 套餐操作，不针对具体SKU
                    'sku_name' => '套餐生产最终确认',
                    'operation_type' => 3, // 新增操作类型：最终套数确认
                    'operator_id' => $operator_info['uid'],
                    'operator_name' => $operator_info['user_name'],
                    'before_whole_stock' => 0,
                    'before_loose_stock' => 0,
                    'after_whole_stock' => 0,
                    'after_loose_stock' => 0,
                    'change_amount' => $final_quantity,
                    'related_id' => $production_id,
                    'remark' => "确认最终生产套数：计划{$production_info['production_quantity']}套，实际{$final_quantity}套"
                ];

                $stock_log_model = new \app\model\pharmacy\PharmacyStockLog();
                $stock_log_model->addLog($log_data);

                // 6. 返回成功结果，包含库存记录信息
                $success_data = [
                    'production_id' => $production_id,
                    'original_quantity' => $production_info['production_quantity'],
                    'final_quantity' => $final_quantity,
                    'confirm_time' => $update_data['quantity_confirm_time'] ?? date('Y-m-d H:i:s'),
                    'stock_records_created' => $stock_result['code'] >= 0 ? true : false
                ];
                
                if ($stock_result['code'] >= 0) {
                    $success_data['stock_info'] = $stock_result['data'];
                }

                return $this->success($success_data);
            } else {
                return $this->error('', '更新最终套数失败');
            }

        } catch (\Exception $e) {
            return $this->error('', '确认最终套数失败：' . $e->getMessage());
        }
    }

    /**
     * 检查字段是否存在
     * @param string $field_name 字段名
     * @return bool
     */
    private function checkFieldExists($field_name)
    {
        try {
            $sql = "SHOW COLUMNS FROM `ns_" . $this->table . "` LIKE '{$field_name}'";
            $result = \think\facade\Db::query($sql);
            return !empty($result);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取未确认最终套数的生产记录
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @return array
     */
    public function getUnconfirmedProductions($condition = [], $page = 1, $page_size = 20)
    {
        try {
            // 添加未确认的条件
            $condition[] = ['quantity_confirmed', '=', 0];

            $offset = ($page - 1) * $page_size;

            $list = \think\facade\Db::name($this->table)
                ->where($condition)
                ->field('id,template_name,production_quantity,operator_name,create_time,total_cost')
                ->order('create_time desc')
                ->limit($offset, $page_size)
                ->select()
                ->toArray();

            $count = \think\facade\Db::name($this->table)
                ->where($condition)
                ->count();

            // 格式化时间
            foreach ($list as &$item) {
                $item['create_time_format'] = date('Y-m-d H:i:s', strtotime($item['create_time']));
            }

            return $this->success([
                'list' => $list,
                'count' => $count,
                'page' => $page,
                'page_size' => $page_size
            ]);

        } catch (\Exception $e) {
            return $this->error('', '获取未确认记录失败：' . $e->getMessage());
        }
    }
}
