<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>拆零操作 - 药房库存管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATIC__/ext/layui/css/layui.css">
    <link rel="stylesheet" href="__STATIC__/shop/css/pharmacy.css">
    <style>
        .split-container {
            min-height: 100vh;
            background-color: #f5f5f5;
            padding: 20px;
        }
        .split-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .split-title {
            font-size: 24px;
            color: #333;
            margin: 0;
        }
        .header-buttons {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .back-btn {
            background: #1E9FFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .back-btn:hover {
            background: #0078D4;
            color: white;
            text-decoration: none;
        }
        .split-main {
            display: flex;
            gap: 20px;
            height: calc(100vh - 140px);
        }
        .split-operation {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .split-records {
            width: 400px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }
        .operation-form {
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 8px;
        }
        .form-group label {
            display: block;
            margin-bottom: 3px;
            font-weight: bold;
            color: #333;
            font-size: 13px;
        }
        .scanner-input {
            width: 100%;
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.3s;
            box-sizing: border-box;
            display: block;
            margin: 0;
        }
        .scanner-input:focus {
            border-color: #1E9FFF;
            outline: none;
        }
        .scanner-input.scanning {
            border-color: #28a745;
            background-color: #f8fff9;
            box-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
        }
        .scanner-input.processing {
            border-color: #ffc107;
            background-color: #fffdf5;
            box-shadow: 0 0 5px rgba(255, 193, 7, 0.3);
        }
        .operation-tips {
            background: #f0f9ff;
            border: 1px solid #b3e5fc;
            border-radius: 6px;
            padding: 12px;
            margin-top: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #0277bd;
        }
        .stock-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            display: none;
        }
        .stock-info.show {
            display: block;
        }
        .stock-info h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .stock-detail {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .stock-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .stock-item .label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        .stock-item .value {
            font-size: 16px;
            font-weight: bold;
            color: #1E9FFF;
        }
        .stock-item.shelf-info {
            background-color: #ff4d4f;
            color: white;
            border-radius: 4px;
            padding: 8px;
        }
        .stock-item.shelf-info .label {
            color: white;
        }
        .stock-item.shelf-info .value {
            color: white;
            font-weight: bold;
        }
        .records-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            border-bottom: 2px solid #1E9FFF;
            padding-bottom: 8px;
            flex-shrink: 0;
        }
        #recentRecords {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }

        /* 待拆零清单样式 */
        .split-summary {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            flex-shrink: 0;
        }
        .summary-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .summary-item .label {
            font-size: 13px;
            color: #666;
        }
        .summary-item .value {
            font-size: 16px;
            font-weight: bold;
            color: #1E9FFF;
        }
        .summary-item .unit {
            font-size: 13px;
            color: #666;
        }

        #sidebarSplitList {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
            margin-bottom: 15px;
        }

        .empty-notice {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
        .empty-notice i {
            font-size: 48px;
            margin-bottom: 10px;
            display: block;
        }
        .empty-notice p {
            margin: 5px 0;
            font-size: 14px;
        }
        .empty-notice .tip {
            font-size: 12px;
            color: #ccc;
        }

        .sidebar-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #1E9FFF;
        }
        .sidebar-item .item-info {
            flex: 1;
        }
        .sidebar-item .item-name {
            font-size: 13px;
            color: #333;
            margin-bottom: 2px;
        }
        .sidebar-item .item-quantity {
            font-size: 12px;
            color: #666;
        }
        .sidebar-item .item-shelf {
            font-size: 11px;
            color: #999;
            margin-top: 2px;
        }
        .sidebar-item .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
        }
        .sidebar-item .remove-btn:hover {
            background: #c82333;
        }

        .split-actions {
            flex-shrink: 0;
        }
        .confirm-btn {
            width: 100%;
            background: #28a745;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .confirm-btn:hover:not(:disabled) {
            background: #218838;
        }
        .confirm-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .record-item {
            padding: 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        .record-item .time {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .record-item .content {
            font-size: 14px;
            color: #333;
        }
        .record-item .result {
            font-size: 12px;
            color: #28a745;
            margin-top: 5px;
        }

        /* 拆零记录弹窗样式 */
        .history-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .history-modal-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            width: 90%;
            max-width: 1000px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
        }
        .history-modal-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .history-modal-title {
            font-size: 18px;
            color: #333;
            margin: 0;
        }
        .history-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .history-modal-close:hover {
            color: #666;
        }
        .history-modal-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        .history-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .filter-group label {
            font-size: 14px;
            color: #666;
            margin: 0;
        }
        .filter-input {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .filter-btn {
            background: #1E9FFF;
            color: white;
            border: none;
            padding: 6px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .filter-btn:hover {
            background: #0d7dd4;
        }
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .history-table th,
        .history-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .history-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .history-table tr:hover {
            background: #f8f9fa;
        }
        .history-table .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .history-table .status.success {
            background: #d4edda;
            color: #155724;
        }
        .history-table .status.failed {
            background: #f8d7da;
            color: #721c24;
        }

        /* 详情弹窗样式 */
        .detail-modal {
            padding: 20px;
        }

        .detail-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #1E9FFF;
        }

        .detail-header h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }

        .detail-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .detail-info p {
            margin: 0;
            padding: 8px 0;
            color: #666;
        }

        .detail-body h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .detail-table th,
        .detail-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .detail-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }

        .detail-table tr:hover {
            background: #f8f9fa;
        }

        .detail-btn {
            background: #1E9FFF;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .detail-btn:hover {
            background: #0d7dd4;
        }
    </style>
</head>
<body>
<div class="split-container">
    <!-- 顶部标题栏 -->
    <div class="split-header">
        <h1 class="split-title">拆零操作</h1>
        <div class="header-buttons">
            <button class="back-btn" id="splitHistoryBtn">
                <i class="layui-icon layui-icon-template-1"></i>
                拆零记录
            </button>
            <a href="{:url('shop/Production/index')}" class="back-btn">
                <i class="layui-icon layui-icon-return"></i>
                返回生产端
            </a>
        </div>
    </div>

    <!-- 主要内容区 -->
    <div class="split-main">
        <!-- 左侧操作区 -->
        <div class="split-operation">
            <div class="operation-form">
                <div class="form-group">
                    <label>扫描或输入SKU编码：</label>
                    <input type="text" class="scanner-input" id="splitSkuInput" placeholder="请扫描或输入SKU编码" autofocus>
                </div>
                
                <div class="operation-tips">
                    <i class="layui-icon layui-icon-tips"></i>
                    <span>扫描后添加到拆零清单，可批量操作后统一确认拆零</span>
                </div>

                <!-- 库存信息显示 -->
                <div class="stock-info" id="stockInfo">
                    <h4 id="skuName">商品名称</h4>
                    <div class="stock-detail">
                        <div class="stock-item">
                            <div class="label">整瓶库存</div>
                            <div class="value" id="wholeStock">0</div>
                        </div>
                        <div class="stock-item">
                            <div class="label">散药库存</div>
                            <div class="value" id="looseStock">0</div>
                        </div>
                        <div class="stock-item">
                            <div class="label">每瓶颗粒</div>
                            <div class="value" id="grainsPerBottle">0</div>
                        </div>
                        <div class="stock-item">
                            <div class="label">总颗粒数</div>
                            <div class="value" id="totalGrains">0</div>
                        </div>
                        <div class="stock-item shelf-info">
                            <div class="label">货架位置</div>
                            <div class="value" id="shelfNo">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧待拆零清单区 -->
        <div class="split-records">
            <div class="records-section">
                <h3>待拆零清单</h3>
                <div class="split-summary">
                    <div class="summary-item">
                        <span class="label">总计：</span>
                        <span class="value" id="sidebarTotalBottles">0</span>
                        <span class="unit">瓶</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">商品种类：</span>
                        <span class="value" id="sidebarTotalProducts">0</span>
                        <span class="unit">种</span>
                    </div>
                </div>
                <div id="sidebarSplitList">
                    <div class="empty-notice">
                        <i class="layui-icon layui-icon-survey"></i>
                        <p>暂无待拆零商品</p>
                        <p class="tip">请扫描SKU编码添加商品</p>
                    </div>
                </div>
                <div class="split-actions">
                    <button class="confirm-btn" id="confirmSplitBtn" disabled>确认拆零</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__STATIC__/js/jquery-3.1.1.js"></script>
<script src="__STATIC__/ext/layui/layui.js"></script>
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 全局变量
    var splitItems = [];
    var recentRecords = [];
    var isProcessing = false;
    var lastProcessedSku = '';

    // 扫描枪检测相关变量
    var scannerTimer = null;
    var lastInputTime = 0;

    // 初始化
    $(document).ready(function() {
        // 忽略扩展程序相关的错误
        window.addEventListener('error', function(e) {
            if (e.message && e.message.includes('Extension context invalidated')) {
                e.preventDefault();
                return false;
            }
        });

        // 忽略未处理的Promise拒绝（通常由扩展程序引起）
        window.addEventListener('unhandledrejection', function(e) {
            if (e.reason && e.reason.message && e.reason.message.includes('Extension')) {
                e.preventDefault();
                return false;
            }
        });

        bindEvents();
        $('#splitSkuInput').focus();
        renderSidebarSplitList();
    });
    
    // 绑定事件
    function bindEvents() {
        // 扫描输入框事件 - 多重事件监听适配扫描枪
        $('#splitSkuInput').on('keypress keydown keyup input paste', function(e) {
            var currentTime = Date.now();
            var inputValue = $(this).val().trim();

            // 处理回车键
            if (e.type === 'keypress' && e.which === 13) {
                e.preventDefault();
                if (inputValue) {
                    processSplitSkuInput(inputValue);
                }
                return;
            }

            // 处理粘贴事件
            if (e.type === 'paste') {
                var self = this;
                setTimeout(function() {
                    var pastedValue = $(self).val().trim();
                    if (pastedValue) {
                        processSplitSkuInput(pastedValue);
                    }
                }, 10);
                return;
            }

            // 扫描枪输入检测逻辑
            if (e.type === 'input') {
                var timeDiff = currentTime - lastInputTime;
                lastInputTime = currentTime;

                // 清除之前的定时器
                if (scannerTimer) {
                    clearTimeout(scannerTimer);
                }

                // 如果输入速度很快（扫描枪特征），或者检测到完整条码
                if (timeDiff < 50 || inputValue.length >= 8) {
                    // 设置短暂延迟，等待扫描枪输入完成
                    scannerTimer = setTimeout(function() {
                        var finalValue = $('#splitSkuInput').val().trim();
                        if (finalValue && finalValue.length >= 4) { // 最少4位字符
                            processSplitSkuInput(finalValue);
                        }
                    }, 100);
                }
            }
        });

        // 输入框失焦时也查询库存（兼容手动输入）
        $('#splitSkuInput').on('blur', function() {
            var sku_no = $(this).val().trim();
            if (sku_no && sku_no.length >= 4) {
                getRealTimeStock(sku_no);
            }
        });

        // 拆零记录按钮
        $('#splitHistoryBtn').on('click', function() {
            showSplitHistory();
        });

        // 确认拆零按钮
        $('#confirmSplitBtn').on('click', function() {
            confirmSplit();
        });
    }
    
    // 获取实时库存
    function getRealTimeStock(sku_no) {
        $.ajax({
            url: '{:url("shop/PharmacyManagement/getRealTimeStock")}',
            type: 'POST',
            data: {sku_no: sku_no},
            success: function(res) {
                if (res.code === 0) {  // 修复：使用正确的成功代码
                    showStockInfo(res.data);
                } else {
                    hideStockInfo();
                    if (res.message) {
                        layer.msg(res.message, {icon: 2});
                    }
                }
            },
            error: function() {
                hideStockInfo();
                layer.msg('获取库存信息失败', {icon: 2});
            }
        });
    }

    // 处理输入的SKU
    function processSplitSkuInput(sku_no) {
        if (isProcessing || sku_no === lastProcessedSku) return; // 防止重复处理

        isProcessing = true;
        lastProcessedSku = sku_no;
        $('#splitSkuInput').addClass('processing');

        // 模拟异步操作
        setTimeout(function() {
            addToSplitList(sku_no);

            // 取消处理状态
            isProcessing = false;
            $('#splitSkuInput').removeClass('processing').val('').focus();
            lastProcessedSku = '';
        }, 200); // 调整延时以适配不同扫描枪
    }

    // 显示库存信息
    function showStockInfo(data) {
        $('#skuName').text(data.sku_info.sku_name);
        $('#wholeStock').text(data.stock_detail.whole_bottles);
        $('#looseStock').text(data.stock_detail.loose_grains);
        $('#grainsPerBottle').text(data.stock_detail.grains_per_bottle);
        $('#totalGrains').text(data.total_grains);
        $('#shelfNo').text(data.stock_detail.shelf_no || '-');
        $('#stockInfo').addClass('show');
    }

    // 隐藏库存信息
    function hideStockInfo() {
        $('#stockInfo').removeClass('show');
    }

    // 添加到拆零清单
    function addToSplitList(sku_no) {
        var quantity = 1; // 拆零固定为1瓶

        $.ajax({
            url: '{:url("shop/PharmacyManagement/getRealTimeStock")}',
            type: 'POST',
            data: {sku_no: sku_no},
            timeout: 10000, // 10秒超时
            success: function(res) {
                try {
                    if (res.code === 0) {
                        var skuInfo = res.data.sku_info;

                        // 检查整瓶库存是否足够
                        if (skuInfo.whole_stock < 1) {
                            layer.msg('整瓶库存不足，无法拆零', {icon: 2});
                            return;
                        }

                        // 检查是否已存在
                        var existingIndex = splitItems.findIndex(item => item.sku_id === skuInfo.sku_id);
                        if (existingIndex >= 0) {
                            splitItems[existingIndex].quantity += quantity;
                        } else {
                            splitItems.push({
                                sku_id: skuInfo.sku_id,
                                sku_no: skuInfo.sku_no,
                                sku_name: skuInfo.sku_name,
                                quantity: quantity,
                                shelf_no: res.data.stock_detail.shelf_no || '-'
                            });
                        }

                        renderSidebarSplitList();
                        showStockInfo(res.data);
                        layer.msg('已添加到拆零清单', {icon: 1, time: 1000});
                    } else {
                        layer.msg(res.message || '获取库存信息失败', {icon: 2});
                    }
                } catch (e) {
                    console.warn('处理响应数据时出错:', e);
                    layer.msg('数据处理失败', {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                // 忽略由扩展程序引起的错误
                if (status !== 'abort' && status !== 'timeout') {
                    console.warn('AJAX请求失败:', status, error);
                    layer.msg('获取库存信息失败', {icon: 2});
                }
            }
        });
    }

    // 渲染待拆零清单
    function renderSidebarSplitList() {
        var totalBottles = 0;
        var productCount = {};
        var sidebarHtml = '';

        if (splitItems.length === 0) {
            sidebarHtml = '<div class="empty-notice">' +
                         '<i class="layui-icon layui-icon-survey"></i>' +
                         '<p>暂无待拆零商品</p>' +
                         '<p class="tip">请扫描SKU编码添加商品</p>' +
                         '</div>';
        } else {
            splitItems.forEach(function(item, index) {
                totalBottles += item.quantity;
                productCount[item.sku_id] = true;

                sidebarHtml += '<div class="sidebar-item">' +
                              '<div class="item-info">' +
                              '<div class="item-name">' + item.sku_name + '</div>' +
                              '<div class="item-quantity">' + item.quantity + '瓶</div>' +
                              '<div class="item-shelf">货架：' + (item.shelf_no || '-') + '</div>' +
                              '</div>' +
                              '<button class="remove-btn" onclick="removeSplitItem(' + index + ')">移除</button>' +
                              '</div>';
            });
        }

        $('#sidebarSplitList').html(sidebarHtml);
        $('#sidebarTotalBottles').text(totalBottles);
        $('#sidebarTotalProducts').text(Object.keys(productCount).length);
        $('#confirmSplitBtn').prop('disabled', splitItems.length === 0);
    }

    // 移除拆零项目
    window.removeSplitItem = function(index) {
        splitItems.splice(index, 1);
        renderSidebarSplitList();
    };

    // 确认拆零
    function confirmSplit() {
        if (splitItems.length === 0) {
            layer.msg('请先添加拆零商品', {icon: 2});
            return;
        }

        layer.confirm('确认拆零 ' + splitItems.length + ' 种商品吗？', {
            btn: ['确认拆零', '取消']
        }, function(index) {
            layer.close(index);

            var loadingIndex = layer.load(2);

            $.ajax({
                url: '{:url("shop/PharmacyManagement/batchSplitOperation")}',
                type: 'POST',
                data: {items: splitItems},
                success: function(res) {
                    layer.close(loadingIndex);
                    if (res.code === 0) {
                        layer.msg('批量拆零成功！', {icon: 1});

                        // 清空拆零清单
                        splitItems = [];
                        renderSidebarSplitList();
                        hideStockInfo();
                    } else {
                        layer.msg(res.message || '拆零失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.close(loadingIndex);
                    layer.msg('拆零失败', {icon: 2});
                }
            });
        });
    }
    
    // 执行拆零操作
    function performSplit(sku_no) {
        var $input = $('#splitSkuInput');
        $input.prop('disabled', true);
        
        $.ajax({
            url: '{:url("shop/PharmacyManagement/splitOperation")}',
            type: 'POST',
            data: {sku_no: sku_no},
            success: function(res) {
                if (res.code === 0) {  // 修复：使用正确的成功代码
                    layer.msg('拆零操作成功', {icon: 1});

                    // 更新库存显示
                    showStockInfo({
                        sku_info: res.data.sku_info,
                        total_grains: res.data.new_stock.total_grains,
                        stock_detail: {
                            whole_bottles: res.data.new_stock.whole_stock,
                            loose_grains: res.data.new_stock.loose_stock,
                            grains_per_bottle: res.data.sku_info.daizhuang
                        }
                    });

                    // 添加操作记录
                    addRecord(res.data.sku_info.sku_name, '拆零操作成功',
                        '整瓶-1，散药+' + res.data.sku_info.daizhuang);

                    // 清空输入框并重新聚焦
                    $input.val('').focus();
                } else {
                    layer.msg(res.message || '拆零操作失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('拆零操作失败', {icon: 2});
            },
            complete: function() {
                $input.prop('disabled', false);
            }
        });
    }
    
    // 添加操作记录
    function addRecord(skuName, operation, result) {
        var now = new Date();
        var timeString = now.getHours().toString().padStart(2, '0') + ':' +
                        now.getMinutes().toString().padStart(2, '0') + ':' +
                        now.getSeconds().toString().padStart(2, '0');
        
        recentRecords.unshift({
            time: timeString,
            content: skuName + ' - ' + operation,
            result: result
        });
        
        // 只保留最近10条记录
        if (recentRecords.length > 10) {
            recentRecords = recentRecords.slice(0, 10);
        }
        
        renderRecords();
    }
    
    // 渲染记录列表
    function renderRecords() {
        var html = '';
        if (recentRecords.length === 0) {
            html = '<div class="record-item"><div class="time">等待操作...</div><div class="content">请扫描SKU编码开始拆零操作</div></div>';
        } else {
            recentRecords.forEach(function(record) {
                html += '<div class="record-item">' +
                       '<div class="time">' + record.time + '</div>' +
                       '<div class="content">' + record.content + '</div>' +
                       '<div class="result">' + record.result + '</div>' +
                       '</div>';
            });
        }
        $('#recentRecords').html(html);
    }

    // 显示拆零记录弹窗
    function showSplitHistory() {
        const modalHtml = `
            <div class="history-modal" id="historyModal">
                <div class="history-modal-content">
                    <div class="history-modal-header">
                        <h2 class="history-modal-title">拆零记录</h2>
                        <button class="history-modal-close" onclick="closeSplitHistory()">&times;</button>
                    </div>
                    <div class="history-modal-body">
                        <div class="history-filters">
                            <div class="filter-group">
                                <label>日期范围：</label>
                                <input type="date" class="filter-input" id="startDate">
                                <span>至</span>
                                <input type="date" class="filter-input" id="endDate">
                            </div>
                            <div class="filter-group">
                                <label>商品名称：</label>
                                <input type="text" class="filter-input" id="productName" placeholder="输入商品名称">
                            </div>
                            <div class="filter-group">
                                <button class="filter-btn" onclick="searchHistory()">查询</button>
                            </div>
                        </div>
                        <div id="historyTableContainer">
                            <div style="text-align: center; padding: 40px; color: #999;">
                                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                                <p>加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);

        // 设置默认日期范围（最近一周）
        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        $('#endDate').val(today.toISOString().split('T')[0]);
        $('#startDate').val(weekAgo.toISOString().split('T')[0]);

        // 自动加载数据
        loadHistoryData();
    }

    // 关闭拆零记录弹窗
    window.closeSplitHistory = function() {
        $('#historyModal').remove();
    };

    // 加载历史数据
    function loadHistoryData() {
        const startDate = $('#startDate').val();
        const endDate = $('#endDate').val();
        const productName = $('#productName').val().trim();

        $.ajax({
            url: '{:url("shop/PharmacyManagement/getSplitOrderList")}',
            type: 'POST',
            data: {
                start_date: startDate,
                end_date: endDate,
                product_name: productName,
                page: 1,
                page_size: 20
            },
            success: function(res) {
                if (res.code === 0) {
                    renderHistoryTable(res);
                } else {
                    $('#historyTableContainer').html('<div style="text-align: center; color: #999; padding: 40px;"><i class="layui-icon layui-icon-face-cry"></i><p>加载失败</p></div>');
                }
            },
            error: function() {
                $('#historyTableContainer').html('<div style="text-align: center; color: #999; padding: 40px;"><i class="layui-icon layui-icon-face-cry"></i><p>网络错误，请稍后重试</p></div>');
            }
        });
    }

    // 渲染历史记录表格
    function renderHistoryTable(data) {
        let tableHtml = `
            <table class="history-table">
                <thead>
                    <tr>
                        <th>拆零单号</th>
                        <th>拆零时间</th>
                        <th>总瓶数</th>
                        <th>产品种类</th>
                        <th>操作员</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>`;

        if (data.data && data.data.length > 0) {
            data.data.forEach(function(item) {
                const statusClass = item.status === 1 ? 'success' : 'failed';
                const statusText = item.status_name || '未知';

                tableHtml += `
                    <tr>
                        <td>${item.order_no}</td>
                        <td>${item.create_time_format}</td>
                        <td>${item.total_bottles}瓶</td>
                        <td>${item.total_products}种</td>
                        <td>${item.operator_name || '系统'}</td>
                        <td><span class="status ${statusClass}">${statusText}</span></td>
                        <td><button class="detail-btn" onclick="viewSplitDetail(${item.id})">详情</button></td>
                    </tr>`;
            });
        } else {
            tableHtml += '<tr><td colspan="7" style="text-align: center; color: #999;">暂无记录</td></tr>';
        }

        tableHtml += '</tbody></table>';

        $('#historyTableContainer').html(tableHtml);
    }

    // 搜索历史记录
    window.searchHistory = function() {
        loadHistoryData();
    };

    // 查看拆零详情
    window.viewSplitDetail = function(orderId) {
        $.ajax({
            url: '{:url("shop/PharmacyManagement/getSplitOrderDetail")}',
            type: 'POST',
            data: { order_id: orderId },
            success: function(res) {
                if (res.code === 0) {
                    showSplitDetailModal(res.data);
                } else {
                    layer.msg(res.message || '获取详情失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请稍后重试', {icon: 2});
            }
        });
    };

    // 显示拆零详情弹窗
    function showSplitDetailModal(orderData) {
        let detailHtml = `
            <div class="detail-modal">
                <div class="detail-header">
                    <h3>拆零单详情</h3>
                    <div class="detail-info">
                        <p><strong>单据号：</strong>${orderData.order_no}</p>
                        <p><strong>拆零时间：</strong>${orderData.create_time_format}</p>
                        <p><strong>操作员：</strong>${orderData.operator_name}</p>
                        <p><strong>状态：</strong>${orderData.status_name}</p>
                    </div>
                </div>
                <div class="detail-body">
                    <h4>拆零明细</h4>
                    <table class="detail-table">
                        <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>SKU编码</th>
                                <th>拆零瓶数</th>
                                <th>每瓶颗粒数</th>
                                <th>总颗粒数</th>
                                <th>操作前整瓶库存</th>
                                <th>操作后整瓶库存</th>
                            </tr>
                        </thead>
                        <tbody>`;

        if (orderData.items && orderData.items.length > 0) {
            orderData.items.forEach(function(item) {
                detailHtml += `
                    <tr>
                        <td>${item.sku_name}</td>
                        <td>${item.sku_no}</td>
                        <td>${item.quantity}瓶</td>
                        <td>${item.grains_per_bottle}颗</td>
                        <td>${item.total_grains}颗</td>
                        <td>${item.before_whole_stock}瓶</td>
                        <td>${item.after_whole_stock}瓶</td>
                    </tr>`;
            });
        } else {
            detailHtml += '<tr><td colspan="7" style="text-align: center;">暂无明细</td></tr>';
        }

        detailHtml += `
                        </tbody>
                    </table>
                </div>
            </div>`;

        layer.open({
            type: 1,
            title: '拆零单详情',
            area: ['900px', '600px'],
            content: detailHtml,
            btn: ['关闭'],
            yes: function(index) {
                layer.close(index);
            }
        });
    }
});
</script>
</body>
</html>
