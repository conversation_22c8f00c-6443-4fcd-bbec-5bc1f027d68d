<?php

namespace app\shop\controller;

use app\model\order\OrderCommon as OrderCommonModel;

/**
 * 生产端控制器
 * Class Production
 * @package app\shop\controller
 */
class Production extends BaseShop
{
    /**
     * 是否需要登录验证
     * true: 需要登录验证
     * false: 不需要登录验证，可直接访问（默认）
     */
    const REQUIRE_LOGIN = true;

    public function __construct()
    {
        if (self::REQUIRE_LOGIN) {
            // 需要登录验证，调用父类构造函数
            parent::__construct();
        } else {
            // 不需要登录验证，跳过BaseShop的登录检查
            \app\Controller::__construct();
            // 手动设置必要的属性
            $this->site_id = request()->siteid() ?: 1;
            $this->uid = 0;
            $this->user_info = [];
        }
    }
    /**
     * 生产端主页面
     */
    public function index()
    {
        $this->assign('page_title', '生产端管理系统');
        $this->assign('right_cards', $this->getRightCards());
        return $this->fetch('production/index');
    }

    /**
     * 订单搜索
     */
    public function search()
    {
        if (request()->isAjax()) {
            $order_no = input('order_no', '');
            
            if (empty($order_no)) {
                return json(['code' => -1, 'msg' => '请输入订单号']);
            }

            $order_common_model = new OrderCommonModel();

            // 根据订单号搜索订单
            $condition = [
                ['order_no', '=', $order_no],
                ['site_id', '=', $this->site_id],
                ['is_delete', '=', 0]
            ];

            $order_info_result = $order_common_model->getOrderInfo($condition);

            if ($order_info_result['code'] < 0 || empty($order_info_result['data'])) {
                return json(['code' => -1, 'msg' => '未找到相关订单']);
            }

            $order_info = $order_info_result['data'];

            // 获取订单详情
            $order_detail_result = $order_common_model->getOrderDetail($order_info['order_id']);

            if ($order_detail_result['code'] < 0 || empty($order_detail_result['data'])) {
                return json(['code' => -1, 'msg' => '订单详情获取失败']);
            }

            $order_detail = $order_detail_result['data'];

            // 构建左侧卡片数据
            $left_cards = $this->buildLeftCards($order_detail);

            return json([
                'code' => 0,
                'msg' => '搜索成功',
                'data' => [
                    'order_info' => $order_detail,
                    'left_cards' => $left_cards
                ]
            ]);
        }
        
        return json(['code' => -1, 'msg' => '非法请求']);
    }

    /**
     * 构建左侧动态卡片数据
     * @param array $order_detail 订单详情
     * @return array
     */
    private function buildLeftCards($order_detail)
    {
        $cards = [];
        $order_id = $order_detail['order_id'];

        // 1. 个性化说明
        $cards[] = [
            'title' => '个性化说明',
            'icon' => 'layui-icon-file-b',
            'color' => 'primary',
            'url' => url('shop/Order/printOrderr', ['order_id' => $order_id, 'type' => 1]),
            'target' => '_blank'
        ];

        // 2. 运单
        $cards[] = [
            'title' => '运单',
            'icon' => 'layui-icon-template-1',
            'color' => 'normal',
            'url' => url('shop/Order/printShunfeng', ['order_id' => $order_id]),
            'target' => '_blank'
        ];

        // 3. 封口单
        $cards[] = [
            'title' => '封口单',
            'icon' => 'layui-icon-print',
            'color' => 'warm',
            'url' => url('shop/Order/printGood', ['order_id' => $order_id]),
            'target' => '_blank',
            'action' => 'seal_print',
            'order_id' => $order_id
        ];

        // 4. 生产确认单
        $cards[] = [
            'title' => '生产确认单',
            'icon' => 'layui-icon-export',
            'color' => 'danger',
            'url' => url('shop/Order/printOrder', ['order_id' => $order_id]),
            'target' => '_blank'
        ];

        return $cards;
    }

    /**
     * 获取右侧静态卡片数据
     * @return array
     */
    public function getRightCards()
    {
        return [
            [
                'title' => '拆零操作',
                'icon' => 'layui-icon-component',
                'color' => 'primary',
                'url' => url('shop/PharmacyManagement/split'),
                'target' => '_self',
                'description' => '药品拆零操作，整瓶转散药'
            ],
            [
                'title' => '生产计划',
                'icon' => 'layui-icon-calendar',
                'color' => 'normal',
                'url' => url('shop/PharmacyManagement/production'),
                'target' => '_self',
                'description' => '制定和查看生产计划'
            ],
            [
                'title' => '入库操作',
                'icon' => 'layui-icon-survey',
                'color' => 'warm',
                'url' => url('shop/PharmacyManagement/stockin'),
                'target' => '_self',
                'description' => '药品入库操作管理'
            ],
            [
                'title' => '今日生产任务',
                'icon' => 'layui-icon-time',
                'color' => 'danger',
                'url' => url('shop/PharmacyManagement/index'),
                'target' => '_self',
                'description' => '查看今日生产任务清单与进度'
            ]
        ];
    }
}
