<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\express\ExpressTemplate as ExpressTemplateModel;
use app\model\goods\Goods as GoodsModel;
use app\model\system\User as UserModel;
use app\model\goods\GoodsAttribute as GoodsAttributeModel;
use app\model\goods\GoodsCategory as GoodsCategoryModel;
use app\model\goods\GoodsEvaluate as GoodsEvaluateModel;
use app\model\goods\GoodsService as GoodsServiceModel;
use app\model\goods\GoodsLabel as GoodsLabelModel;
use app\model\order\OrderCommon as OrderCommonModel;
use app\model\order\OrderExport;
use think\Exception;

/**
 * 实物商品
 * Class Goods
 * @package app\shop\controller
 */
class Goods extends BaseShop
{

    public function __construct()
    {
        //执行父类构造函数
        parent::__construct();
    }

    /**
     * 商品列表
     * @return mixed
     */
    public function lists()
    {
        $goods_model = new GoodsModel();
        if (request()->isAjax()) {
            $page_index  = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $search_text = input('search_text', "");
            //$goods_state = 1;
            $goods_state = input('goods_state', "");
            $start_sale  = input('start_sale', 0);
            $end_sale    = input('end_sale', 0);
            $start_price = input('start_price', 0);
            $end_price   = input('end_price', 0);
            $goods_class = input('goods_class', "");
            $label_id    = input('label_id', "");

            $promotion_type = input('promotion_type', "");

            $condition = [['is_delete', '=', 0], ['site_id', '=', $this->site_id]];

            if (!empty($search_text)) {
                $condition[] = ['goods_name|goods_chi', 'like', '%' . $search_text . '%'];
            }
            $category_id = input('category_id', "");
            if (!empty($category_id)) {
                $condition[] = ['category_id', 'like', '%,' . $category_id . '%'];
            }

            if ($goods_class !== "") {
                $condition[] = ['goods_class', '=', $goods_class];
            }

            if (!empty($label_id)) {
                $condition[] = ['label_id', '=', $label_id];
            }

            if (!empty($promotion_type)) {
                $condition[] = ['promotion_addon', 'like', "%{$promotion_type}%"];
            }

            // 上架状态
            if ($goods_state !== '') {
                if ($goods_state==2){
                    $ress                  = $goods_model->getbaojingGood();
                    $condition[] = ['goods_id', 'in', $ress];
                }else{
                    $condition[] = ['goods_state', '=', $goods_state];
                }
            }
            if (!empty($start_sale)) $condition[] = ['sale_num', '>=', $start_sale];
            if (!empty($end_sale)) $condition[] = ['sale_num', '<=', $end_sale];
            if (!empty($start_price)) $condition[] = ['price', '>=', $start_price];
            if (!empty($end_price)) $condition[] = ['price', '<=', $end_price];
            $res                  = $goods_model->getGoodsPageList($condition, $page_index, $page_size);
            $goods_promotion_type = event('GoodsPromotionType');
            if (!empty($res['data']['list'])) {
                foreach ($res['data']['list'] as $k => $v) {
                    if (!empty($v['promotion_addon'])) {
                        $v['promotion_addon'] = json_decode($v['promotion_addon'], true);
                        foreach ($v['promotion_addon'] as $ck => $cv) {
                            foreach ($goods_promotion_type as $gk => $gv) {
                                if ($gv['type'] == $ck) {
                                    $res['data']['list'][$k]['promotion_addon_list'][] = $gv;
                                    break;
                                }
                            }
                        }
                    }
                    //获取分类名称
                    $category_json  = json_decode($v['category_json']);
                    $goods_category = '';
                    foreach ($category_json as $kk => $vo) {
                        if (!empty($vo)) {
                            $category_name      = model('goods_category')->getColumn([['category_id', 'in', $vo]], 'category_name');
                            $category_name      = implode('/', $category_name);
                            $goods_category     = $category_name. '  ' .$goods_category;
                        }
                    }
                    $res['data']['list'][$k]['goods_category'] = $goods_category;
                }
            }
            return $res;
        } else {

            // 商品分组
            $goods_label_model = new GoodsLabelModel();
            $label_list        = $goods_label_model->getLabelList([['site_id', '=', $this->site_id]], 'id,label_name', 'create_time desc');
            $label_list        = $label_list['data'];
            $this->assign("label_list", $label_list);

            // 商品服务
            $goods_service_model = new GoodsServiceModel();
            $service_list        = $goods_service_model->getServiceList([['site_id', '=', $this->site_id]], 'id,service_name');
            $service_list        = $service_list['data'];
            $this->assign("service_list", $service_list);

            //获取运费模板
            $express_template_model = new ExpressTemplateModel();
            $express_template_list  = $express_template_model->getExpressTemplateList([['site_id', "=", $this->site_id]], 'template_id,template_name', 'is_default desc');
            $express_template_list  = $express_template_list['data'];
            $this->assign("express_template_list", $express_template_list);

            //判断会员价插件
            $memberprice_is_exit = addon_is_exit('memberprice', $this->site_id);
            $this->assign('memberprice_is_exit', $memberprice_is_exit);
            // 营销活动
            $goods_promotion_type = event('GoodsPromotionType');
            $this->assign('promotion_type', $goods_promotion_type);
            return $this->fetch("goods/lists");
        }
    }

    public function stocklists()
    {
        $goods_model = new GoodsModel();
        if (request()->isAjax()) {
            $page_index  = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $search_text = input('search_text', "");
            $goods_state = input('goods_state', "");
            $start_sale  = input('start_sale', 0);
            $end_sale    = input('end_sale', 0);
            $start_price = input('start_price', 0);
            $end_price   = input('end_price', 0);
            $goods_class = input('goods_class', "");
            $label_id    = input('label_id', "");

            $promotion_type = input('promotion_type', "");

            $condition = [['g.is_delete', '=', 0], ['g.site_id', '=', $this->site_id]];

            if (!empty($search_text)) {
                $condition[] = ['g.goods_name|g.goods_chi', 'like', '%' . $search_text . '%'];
            }
            $category_id = input('g.category_id', "");
            if (!empty($category_id)) {
                $condition[] = ['g.category_id', 'like', '%,' . $category_id . '%'];
            }

            if ($goods_class !== "") {
                $condition[] = ['g.goods_class', '=', $goods_class];
            }

            if (!empty($label_id)) {
                $condition[] = ['g.label_id', '=', $label_id];
            }

            if (!empty($promotion_type)) {
                $condition[] = ['g.promotion_addon', 'like', "%{$promotion_type}%"];
            }

            // 上架状态
            if ($goods_state !== '') {
                if ($goods_state==2){
                    $ress                  = $goods_model->getbaojingGood();
                    $condition[] = ['g.goods_id', 'in', $ress];
                }else{
                    $condition[] = ['g.goods_state', '=', $goods_state];
                }
            }
            if (!empty($start_sale)) $condition[] = ['g.sale_num', '>=', $start_sale];
            if (!empty($end_sale)) $condition[] = ['g.sale_num', '<=', $end_sale];
            if (!empty($start_price)) $condition[] = ['g.price', '>=', $start_price];
            if (!empty($end_price)) $condition[] = ['g.price', '<=', $end_price];

            $condition[] = ['gs.is_delete', '=', 0];
            $field       = 'g.*,gs.stock,gs.daizhuang,gs.sku_id,gs.sku_no,gs.whole_stock,gs.loose_stock';

            $alias = 'gs';
            $join  = [
                ['goods g', 'gs.sku_id = g.sku_id', 'inner']
            ];

            $res  = $goods_model->getGoodsSkuPageList($condition, $page_index, $page_size, 'g.create_time desc', $field, $alias, $join);

            $alias       = 'og';
            $join        = [
                [
                    'order_goods o',
                    'o.order_id = og.order_id',
                    'left'
                ]
            ];
            //销售量
            $salelist = model('order')->getList([['og.order_status','>',0],['og.create_time','>',(time()-60*60*24*30)],['o.sku_id', 'in', array_column($res['data']['list'],'sku_id')]], 'o.sku_id,o.num', '', $alias, $join);
            $salelistresul = [];
            foreach($salelist as $k=>$vo){
                if (!isset($salelistresul[$vo['sku_id']])){
                    $salelistresul[$vo['sku_id']] = 0;
                }

                $salelistresul[$vo['sku_id']] = $salelistresul[$vo['sku_id']] + $vo['num'];

            }
            $goods_promotion_type = event('GoodsPromotionType');
            if (!empty($res['data']['list'])) {
                foreach ($res['data']['list'] as $k => $v) {
                    $res['data']['list'][$k]['bisales'] = isset($salelistresul[$v['sku_id']])?$salelistresul[$v['sku_id']]:0;
                    if (!empty($v['promotion_addon'])) {
                        $v['promotion_addon'] = json_decode($v['promotion_addon'], true);
                        foreach ($v['promotion_addon'] as $ck => $cv) {
                            foreach ($goods_promotion_type as $gk => $gv) {
                                if ($gv['type'] == $ck) {
                                    $res['data']['list'][$k]['promotion_addon_list'][] = $gv;
                                    break;
                                }
                            }
                        }
                    }
                    //获取分类名称
                    $category_json  = json_decode($v['category_json']);
                    $goods_category = '';
                    foreach ($category_json as $kk => $vo) {
                        if (!empty($vo)) {
                            $category_name      = model('goods_category')->getColumn([['category_id', 'in', $vo]], 'category_name');
                            $category_name      = implode('/', $category_name);
                            $goods_category     = $category_name. '  ' .$goods_category;
                        }
                    }
                    $res['data']['list'][$k]['goods_category'] = $goods_category;
                }
                $user_model = new UserModel();
                $sorted = $user_model->array_orderby($res['data']['list'], 'bisales', SORT_DESC);
                $res['data']['list']=$sorted;
            }
            return $res;
        } else {

            // 商品分组
            $goods_label_model = new GoodsLabelModel();
            $label_list        = $goods_label_model->getLabelList([['site_id', '=', $this->site_id]], 'id,label_name', 'create_time desc');
            $label_list        = $label_list['data'];
            $this->assign("label_list", $label_list);

            // 商品服务
            $goods_service_model = new GoodsServiceModel();
            $service_list        = $goods_service_model->getServiceList([['site_id', '=', $this->site_id]], 'id,service_name');
            $service_list        = $service_list['data'];
            $this->assign("service_list", $service_list);

            //获取运费模板
            $express_template_model = new ExpressTemplateModel();
            $express_template_list  = $express_template_model->getExpressTemplateList([['site_id', "=", $this->site_id]], 'template_id,template_name', 'is_default desc');
            $express_template_list  = $express_template_list['data'];
            $this->assign("express_template_list", $express_template_list);

            //判断会员价插件
            $memberprice_is_exit = addon_is_exit('memberprice', $this->site_id);
            $this->assign('memberprice_is_exit', $memberprice_is_exit);
            // 营销活动
            $goods_promotion_type = event('GoodsPromotionType');
            $this->assign('promotion_type', $goods_promotion_type);
            return $this->fetch("goods/stocklists");
        }
    }

    /**
     * 订单导出（已订单为主）
     */
    public function exportGoods()
    {
        $search_text = input('search_text', "");
        $goods_state = input('goods_state', "");
        $start_sale  = input('start_sale', 0);
        $end_sale    = input('end_sale', 0);
        $start_price = input('start_price', 0);
        $end_price   = input('end_price', 0);
        $goods_class = input('goods_class', "");
        $label_id    = input('label_id', "");

        $promotion_type = input('promotion_type', "");

        $condition = [['g.is_delete', '=', 0], ['g.site_id', '=', $this->site_id]];

        if (!empty($search_text)) {
            $condition[] = ['g.goods_name', 'like', '%' . $search_text . '%'];
        }
        $category_id = input('category_id', "");
        if (!empty($category_id)) {
            $condition[] = ['g.category_id', 'like', '%,' . $category_id . '%'];
        }

        if ($goods_class !== "") {
            $condition[] = ['g.goods_class', '=', $goods_class];
        }

        if (!empty($label_id)) {
            $condition[] = ['g.label_id', '=', $label_id];
        }

        if (!empty($promotion_type)) {
            $condition[] = ['g.promotion_addon', 'like', "%{$promotion_type}%"];
        }

        // 上架状态
        if ($goods_state !== '') {
            $condition[] = ['g.goods_state', '=', $goods_state];
        }
        if (!empty($start_sale)) $condition[] = ['g.sale_num', '>=', $start_sale];
        if (!empty($end_sale)) $condition[] = ['g.sale_num', '<=', $end_sale];
        if (!empty($start_price)) $condition[] = ['g.price', '>=', $start_price];
        if (!empty($end_price)) $condition[] = ['g.price', '<=', $end_price];

        $field = [
            'goods_id'=>'商品编号',
            'category_name'=>'品牌',
            'goods_name'=>'商品名称',
            'goods_chi'=>'中文名称',
            'sku_no'=>'商品编码',
            'goods_class_name'=>'商品类型',
            'goods_stock'=>'商品库存',
            'price'=>'商品单价',
            'shelf_no'=>'货架号',
            'daizhuang'=>'规格',
            'introd'=>'商品成分'
        ];
        //接收需要展示的字段
        $input_field = input('field', implode(',', array_keys($field)));
        $goods_model = new GoodsModel();
        $field = 'g.*,gs.sku_no,gs.shelf_no,gs.daizhuang';

        $alias = 'gs';
        $join  = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];
        $order  = $goods_model->getGoodsSkuList($condition, $field, 'g.goods_id desc',null,$alias,$join);

        $header_arr  = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',
            'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ'
        );

        $input_field = explode(',', $input_field);
        //处理数据

        if (!empty($order['data'])) {
            foreach ($order['data'] as $key=>$vo){
                $category_name='';
                if ($vo['category_id']) {
                    $category_name = model('goods_category')->getColumn([['category_id', 'in', $vo['category_id']], ['pid', '=', 55]], 'category_name');
                    if (is_array($category_name)) {
                        $category_name = implode('/', $category_name);
                    }
                }
                $order['data'][$key]['category_name'] = $category_name;
            }

        }


        $order_export_model = new OrderExport();
        if (!empty($order['data'])) {
            $order_list = $order_export_model->handleData($order['data'], $input_field);
        }

        $count = count($input_field);
        // 实例化excel
        $phpExcel = new \PHPExcel();

        $phpExcel->getProperties()->setTitle("商品信息");
        $phpExcel->getProperties()->setSubject("商品信息");
        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);

        $field = [
            'goods_id'=>'商品编号',
            'category_name'=>'品牌',
            'goods_name'=>'商品名称',
            'goods_chi'=>'中文名称',
            'sku_no'=>'商品编码',
            'goods_class_name'=>'商品类型',
            'goods_stock'=>'商品库存',
            'price'=>'商品单价',
            'shelf_no'=>'货架号',
            'daizhuang'=>'规格',
            'introd'=>'商品成分'
        ];
        for ($i = 0; $i < $count; $i++) {
            $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . '1', $field[$input_field[$i]]);
        }

        if (!empty($order_list)) {
            foreach ($order_list as $k => $v) {
                $start = $k + 2;
                for ($i = 0; $i < $count; $i++) {

                    $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . $start, $v[$input_field[$i]]);
                }
            }
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle('商品信息');
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file      = date('Y年m月d日-商品信息', time()) . '.xlsx';
        $objWriter->save($file);

        header("Content-type:application/octet-stream");

        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;

    }

    public function exportGoodsattr()
    {
        $search_text = input('search_text', "");
        $goods_state = input('goods_state', "");
        $start_sale  = input('start_sale', 0);
        $end_sale    = input('end_sale', 0);
        $start_price = input('start_price', 0);
        $end_price   = input('end_price', 0);
        $goods_class = input('goods_class', "");
        $label_id    = input('label_id', "");

        $promotion_type = input('promotion_type', "");

        $condition = [['is_delete', '=', 0], ['site_id', '=', $this->site_id]];

        if (!empty($search_text)) {
            $condition[] = ['goods_name', 'like', '%' . $search_text . '%'];
        }
        $category_id = input('category_id', "");
        if (!empty($category_id)) {
            $condition[] = ['category_id', 'like', '%,' . $category_id . '%'];
        }

        if ($goods_class !== "") {
            $condition[] = ['goods_class', '=', $goods_class];
        }

        if (!empty($label_id)) {
            $condition[] = ['label_id', '=', $label_id];
        }

        if (!empty($promotion_type)) {
            $condition[] = ['promotion_addon', 'like', "%{$promotion_type}%"];
        }

        // 上架状态
        if ($goods_state !== '') {
            $condition[] = ['goods_state', '=', $goods_state];
        }
        if (!empty($start_sale)) $condition[] = ['sale_num', '>=', $start_sale];
        if (!empty($end_sale)) $condition[] = ['sale_num', '<=', $end_sale];
        if (!empty($start_price)) $condition[] = ['price', '>=', $start_price];
        if (!empty($end_price)) $condition[] = ['price', '<=', $end_price];

        $field = [
            'goods_id'=>'商品编号',
            'category_name'=>'品牌',
            'goods_name'=>'商品名称',
            'goods_chi'=>'中文名称',
            'goods_class_name'=>'商品类型',
            'goods_stock'=>'商品库存',
            'sale_num'=>'商品销量',
            'introd'=>'商品成分',
            'attr_name'=>'成分名称',
            'attr_value_name'=>'成分含量'
        ];
        //接收需要展示的字段
        $input_field = input('field', implode(',', array_keys($field)));
        $goods_model = new GoodsModel();
        $order       = $goods_model->getGoodsList($condition, '*', 'goods_id desc');
        $header_arr  = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',
            'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ'
        );

        $input_field = explode(',', $input_field);
        //处理数据
        $result = [];
        if (!empty($order['data'])) {
            foreach ($order['data'] as $key=>$vo){
                $category_name='';
                if ($vo['category_id']) {
                    $category_name = model('goods_category')->getColumn([['category_id', 'in', $vo['category_id']], ['pid', '=', 55]], 'category_name');
                    if (is_array($category_name)) {
                        $category_name = implode('/', $category_name);
                    }
                }
                $order['data'][$key]['category_name'] = $category_name;
                //属性
                $order['data'][$key]['goods_attr_format'] = json_decode($order['data'][$key]['goods_attr_format'],true);
                $order['data'][$key]['attr_name']='';
                $order['data'][$key]['attr_value_name']='';
                $result[]=$order['data'][$key];
                if($order['data'][$key]['goods_attr_format']) {
                    foreach ($order['data'][$key]['goods_attr_format'] as $kk => $vv) {
                        if ($vv['attr_value_name']) {
                            $new_one = [
                                'goods_id' => '',
                                'category_name' => '',
                                'goods_name' => '',
                                'goods_chi' => '',
                                'goods_class_name' => '',
                                'goods_stock' => '',
                                'sale_num' => '',
                                'introd' => '',
                                'attr_name' => $vv['attr_name'],
                                'attr_value_name' => $vv['attr_value_name']
                            ];
                            $result[] = $new_one;
                        }
                    }
                }
            }
            $order['data'] = $result;
        }


        $order_export_model = new OrderExport();
        if (!empty($order['data'])) {
            $order_list = $order_export_model->handleData($order['data'], $input_field);
        }

        $count = count($input_field);
        // 实例化excel
        $phpExcel = new \PHPExcel();

        $phpExcel->getProperties()->setTitle("商品信息");
        $phpExcel->getProperties()->setSubject("商品信息");
        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);

        $field = [
            'goods_id'=>'商品编号',
            'category_name'=>'品牌',
            'goods_name'=>'商品名称',
            'goods_chi'=>'中文名称',
            'goods_class_name'=>'商品类型',
            'goods_stock'=>'商品库存',
            'sale_num'=>'商品销量',
            'introd'=>'商品成分',
            'attr_name'=>'成分名称',
            'attr_value_name'=>'成分含量'
        ];
        for ($i = 0; $i < $count; $i++) {
            $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . '1', $field[$input_field[$i]]);
        }

        if (!empty($order_list)) {
            foreach ($order_list as $k => $v) {
                $start = $k + 2;
                for ($i = 0; $i < $count; $i++) {

                    $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . $start, $v[$input_field[$i]]);
                }
            }
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle('商品信息');
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file      = date('Y年m月d日-商品信息', time()) . '.xlsx';
        $objWriter->save($file);

        header("Content-type:application/octet-stream");

        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;

    }

    /**
     * 添加商品
     * @return mixed
     */
    public function addGoods()
    {
        if (request()->isAjax()) {
            $goods_name       = input("goods_name", "");// 商品名称
            $goods_chi        = input("goods_chi", "");// 商品名称
            $goods_attr_class = input("goods_attr_class", "");// 商品类型id
            $goods_attr_name  = input("goods_attr_name", "");// 商品类型名称
            $category_id      = input("category_id", 0);// 分类id
            $category_json    = json_encode($category_id);//分类字符串
            $category_id      = ',' . implode(',', $category_id) . ',';

            $goods_image       = input("goods_image", "");// 商品主图路径
            $goods_content     = input("goods_content", "");// 商品详情
            $goods_state       = input("goods_state", "");// 商品状态（1.正常0下架）
            $goods_stock       = input("goods_stock", 0);// 商品库存（总和）
            $goods_stock_alarm = input("goods_stock_alarm", 0);// 库存预警
            $is_free_shipping  = input("is_free_shipping", 1);// 是否免邮
            $shipping_template = input("shipping_template", 0);// 指定运费模板
            $goods_spec_format = input("goods_spec_format", "");// 商品规格格式
            $goods_attr_format = input("goods_attr_format", "");// 商品属性格式
            $introduction      = input("introduction", "");// 促销语
            $introd            = input("introd", "");
            $tip               = input("tip", "");
            $keywords          = input("keywords", "");// 关键词
            $unit              = input("unit", "");// 单位
            $sort              = input("sort", 0);// 排序
            $virtual_sale      = input("virtual_sale", 0);// 虚拟销量
            $max_buy           = input("max_buy", 0);// 限购
            $min_buy           = input("min_buy", 0);// 起售

            $video_url         = input("video_url", "");// 视频
            $goods_sku_data    = input("goods_sku_data", "");// SKU商品数据
            $goods_service_ids = input("goods_service_ids", '');// 商品服务id集合
            $label_id          = input("label_id", '');// 商品分组id

            //单规格需要
            $price        = input("price", 0);// 商品价格（取第一个sku）
            $market_price = input("market_price", 0);// 市场价格（取第一个sku）
            $cost_price   = input("cost_price", 0);// 成本价（取第一个sku）
            $sku_no       = input("sku_no", "");// 商品sku编码
            $weight       = input("weight", "");// 重量
            $volume       = input("volume", "");// 体积
            $daizhuang    = input("daizhuang", "");// 重量
            $shelf_no     = input("shelf_no", "");// 体积

            $data = [
                'goods_name'        => $goods_name,
                'goods_chi'         => $goods_chi,
                'goods_attr_class'  => $goods_attr_class,
                'goods_attr_name'   => $goods_attr_name,
                'site_id'           => $this->site_id,
                'category_id'       => $category_id,
                'category_json'     => $category_json,
                'goods_image'       => $goods_image,
                'goods_content'     => $goods_content,
                'goods_state'       => $goods_state,
                'price'             => $price,
                'market_price'      => $market_price,
                'cost_price'        => $cost_price,
                'sku_no'            => $sku_no,
                'weight'            => $weight,
                'volume'            => $volume,
                'daizhuang'         => $daizhuang,
                'shelf_no'          => $shelf_no,
                'goods_stock'       => $goods_stock,
                'goods_stock_alarm' => $goods_stock_alarm,
                'is_free_shipping'  => $is_free_shipping,
                'shipping_template' => $shipping_template,
                'goods_spec_format' => $goods_spec_format,
                'goods_attr_format' => $goods_attr_format,
                'introduction'      => $introduction,
                'introd'            => $introd,
                'tip'               => $tip,
                'keywords'          => $keywords,
                'unit'              => $unit,
                'sort'              => $sort,
                'video_url'         => $video_url,
                'goods_sku_data'    => $goods_sku_data,
                'goods_service_ids' => $goods_service_ids,
                'label_id'          => $label_id,
                'virtual_sale'      => $virtual_sale,
                'max_buy'           => $max_buy,
                'min_buy'           => $min_buy
            ];

            $goods_model = new GoodsModel();
            $res         = $goods_model->addGoods($data);
            return $res;
        } else {

            //获取一级商品分类
            $goods_category_model = new GoodsCategoryModel();
            $condition            = [
                ['pid', '=', 0],
                ['site_id', '=', $this->site_id]
            ];

            $goods_category_list = $goods_category_model->getCategoryList($condition, 'category_id,category_name,level,commission_rate');
            $goods_category_list = $goods_category_list['data'];
            $this->assign("goods_category_list", $goods_category_list);

            //获取运费模板
            $express_template_model = new ExpressTemplateModel();
            $express_template_list  = $express_template_model->getExpressTemplateList([['site_id', "=", $this->site_id]], 'template_id,template_name', 'is_default desc');
            $express_template_list  = $express_template_list['data'];
            $this->assign("express_template_list", $express_template_list);

            //获取商品类型
            $goods_attr_model = new GoodsAttributeModel();
            $attr_class_list  = $goods_attr_model->getAttrClassList([['site_id', '=', $this->site_id]], 'class_id,class_name');
            $attr_class_list  = $attr_class_list['data'];
            $this->assign("attr_class_list", $attr_class_list);

            // 商品服务
            $goods_service_model = new GoodsServiceModel();
            $service_list        = $goods_service_model->getServiceList([['site_id', '=', $this->site_id]], 'id,service_name');
            $service_list        = $service_list['data'];
            $this->assign("service_list", $service_list);

            // 商品分组
            $goods_label_model = new GoodsLabelModel();
            $label_list        = $goods_label_model->getLabelList([['site_id', '=', $this->site_id]], 'id,label_name', 'create_time desc');
            $label_list        = $label_list['data'];
            $this->assign("label_list", $label_list);

            return $this->fetch("goods/add_goods");
        }
    }

    /**
     * 编辑商品
     * @return mixed
     */
    public function editGoods()
    {
        $goods_model = new GoodsModel();
        if (request()->isAjax()) {
            $goods_id         = input("goods_id", 0);// 商品id
            $goods_name       = input("goods_name", "");// 商品名称
            $goods_chi       = input("goods_chi", "");// 商品名称
            $goods_attr_class = input("goods_attr_class", "");// 商品类型id
            $goods_attr_name  = input("goods_attr_name", "");// 商品类型名称
            $category_id      = input("category_id", 0);// 分类id
            $category_json    = json_encode($category_id);//分类字符串
            $category_id      = ',' . implode(',', $category_id) . ',';

            $goods_image       = input("goods_image", "");// 商品主图路径
            $goods_imagc       = input("goods_imagc", "");// 商品主图路径
            $goods_content     = input("goods_content", "");// 商品详情
            $goods_state       = input("goods_state", "");// 商品状态（1.正常0下架）
            $goods_stock       = input("goods_stock", 0);// 商品库存（总和）
            $goods_stock_alarm = input("goods_stock_alarm", 0);// 库存预警
            $is_free_shipping  = input("is_free_shipping", 1);// 是否免邮
            $shipping_template = input("shipping_template", 0);// 指定运费模板
            $goods_spec_format = input("goods_spec_format", "");// 商品规格格式
            $goods_attr_format = input("goods_attr_format", "");// 商品属性格式
            $introduction      = input("introduction", "");// 促销语
            $introd            = input("introd", "");// 促销语
            $tip               = input("tip", "");// 促销语
            $keywords          = input("keywords", "");// 关键词
            $unit              = input("unit", "");// 单位
            $sort              = input("sort", 0);// 排序
            $video_url         = input("video_url", "");// 视频
            $goods_sku_data    = input("goods_sku_data", "");// SKU商品数据
            $goods_service_ids = input("goods_service_ids", '');// 商品服务id集合
            $label_id          = input("label_id", '');// 商品分组id
            $virtual_sale      = input("virtual_sale", 0);// 虚拟销量
            $max_buy           = input("max_buy", 0);// 限购
            $min_buy           = input("min_buy", 0);// 起售

            //单规格需要
            $price        = input("price", 0);// 商品价格（取第一个sku）
            $market_price = input("market_price", 0);// 市场价格（取第一个sku）
            $cost_price   = input("cost_price", 0);// 成本价（取第一个sku）
            $sku_no       = input("sku_no", "");// 商品sku编码
            $weight       = input("weight", "");// 重量
            $volume       = input("volume", "");// 体积
            $daizhuang    = input("daizhuang", "");// 重量
            $shelf_no     = input("shelf_no", "");// 体积

            $data = [
                'goods_id'          => $goods_id,
                'goods_name'        => $goods_name,
                'goods_chi'         => $goods_chi,
                'goods_attr_class'  => $goods_attr_class,
                'goods_attr_name'   => $goods_attr_name,
                'site_id'           => $this->site_id,
                'category_id'       => $category_id,
                'category_json'     => $category_json,
                'goods_image'       => $goods_image,
                'goods_imagc'       => $goods_imagc,
                'goods_content'     => $goods_content,
                'goods_state'       => $goods_state,
                'price'             => $price,
                'market_price'      => $market_price,
                'cost_price'        => $cost_price,
                'sku_no'            => $sku_no,
                'weight'            => $weight,
                'volume'            => $volume,
                'daizhuang'         => $daizhuang,
                'shelf_no'          => $shelf_no,
                'goods_stock'       => $goods_stock,
                'goods_stock_alarm' => $goods_stock_alarm,
                'is_free_shipping'  => $is_free_shipping,
                'shipping_template' => $shipping_template,
                'goods_spec_format' => $goods_spec_format,
                'goods_attr_format' => $goods_attr_format,
                'introduction'      => $introduction,
                'introd'            => $introd,
                'tip'               => $tip,
                'keywords'          => $keywords,
                'unit'              => $unit,
                'sort'              => $sort,
                'video_url'         => $video_url,
                'goods_sku_data'    => $goods_sku_data,
                'goods_service_ids' => $goods_service_ids,
                'label_id'          => $label_id,
                'virtual_sale'      => $virtual_sale,
                'max_buy'           => $max_buy,
                'min_buy'           => $min_buy
            ];
            $res  = $goods_model->editGoods($data);
            return $res;
        } else {

            $goods_id   = input("goods_id", 0);
            $goods_info = $goods_model->editGetGoodsInfo([['goods_id', '=', $goods_id], ['site_id', '=', $this->site_id]]);
            $goods_info = $goods_info['data'];

            $goods_sku_list         = $goods_model->getGoodsSkuList([['goods_id', '=', $goods_id], ['site_id', '=', $this->site_id]], "sku_id,sku_name,sku_no,sku_spec_format,price,market_price,cost_price,stock,weight,volume,sku_image,sku_images,goods_spec_format,spec_name,daizhuang,shelf_no", '');
            $goods_sku_list         = $goods_sku_list['data'];
            $goods_info['sku_list'] = $goods_sku_list;
            $this->assign("goods_info", $goods_info);

            //获取一级商品分类
            $goods_category_model = new GoodsCategoryModel();
            $condition            = [
                ['pid', '=', 0],
                ['site_id', '=', $this->site_id]
            ];

            $goods_category_list = $goods_category_model->getCategoryList($condition, 'category_id,category_name,level,commission_rate');
            $goods_category_list = $goods_category_list['data'];
            $this->assign("goods_category_list", $goods_category_list);

            //获取运费模板
            $express_template_model = new ExpressTemplateModel();
            $express_template_list  = $express_template_model->getExpressTemplateList([['site_id', "=", $this->site_id]], 'template_id,template_name', 'is_default desc');
            $express_template_list  = $express_template_list['data'];
            $this->assign("express_template_list", $express_template_list);

            //获取商品类型
            $goods_attr_model = new GoodsAttributeModel();
            $attr_class_list  = $goods_attr_model->getAttrClassList([['site_id', '=', $this->site_id]], 'class_id,class_name');
            $attr_class_list  = $attr_class_list['data'];
            $this->assign("attr_class_list", $attr_class_list);

            // 商品服务
            $goods_service_model = new GoodsServiceModel();
            $service_list        = $goods_service_model->getServiceList([['site_id', '=', $this->site_id]], 'id,service_name');
            $service_list        = $service_list['data'];
            $this->assign("service_list", $service_list);

            // 商品分组
            $goods_label_model = new GoodsLabelModel();
            $label_list        = $goods_label_model->getLabelList([['site_id', '=', $this->site_id]], 'id,label_name', 'create_time desc');
            $label_list        = $label_list['data'];
            $this->assign("label_list", $label_list);

            return $this->fetch("goods/edit_goods");
        }
    }

    /**
     * 删除商品
     */
    public function deleteGoods()
    {
        if (request()->isAjax()) {
            $goods_ids   = input("goods_ids", 0);
            $goods_model = new GoodsModel();
            $res         = $goods_model->modifyIsDelete($goods_ids, 1, $this->site_id);
            return $res;
        }

    }

    /**
     * 商品回收站
     */
    public function recycle()
    {
        if (request()->isAjax()) {
            $page_index  = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $search_keys = input('search_keys', "");
            $condition   = [['is_delete', '=', 1], ['site_id', "=", $this->site_id]];
            if (!empty($search_keys)) {
                $condition[] = ['goods_name', 'like', '%' . $search_keys . '%'];
            }
            $goods_model = new GoodsModel();
            $res         = $goods_model->getGoodsPageList($condition, $page_index, $page_size);
            return $res;
        } else {
            return $this->fetch("goods/recycle");
        }
    }

    /**
     * 商品回收站商品删除
     */
    public function deleteRecycleGoods()
    {
        if (request()->isAjax()) {
            $goods_ids   = input("goods_ids", 0);
            $goods_model = new GoodsModel();
            $res         = $goods_model->deleteRecycleGoods($goods_ids, $this->site_id);
            return $res;
        }
    }

    /**
     * 商品回收站商品恢复
     */
    public function recoveryRecycle()
    {
        if (request()->isAjax()) {
            $goods_ids   = input("goods_ids", 0);
            $goods_model = new GoodsModel();
            $res         = $goods_model->modifyIsDelete($goods_ids, 0, $this->site_id);
            return $res;
        }

    }

    /**
     * 商品下架
     */
    public function offGoods()
    {
        if (request()->isAjax()) {
            $goods_ids   = input("goods_ids", 0);
            $goods_state = input("goods_state", 0);
            $goods_model = new GoodsModel();
            $res         = $goods_model->modifyGoodsState($goods_ids, $goods_state, $this->site_id);
            return $res;
        }

    }

    /**
     * 商品上架
     */
    public function onGoods()
    {
        if (request()->isAjax()) {
            $goods_ids   = input("goods_ids", 0);
            $goods_state = input("goods_state", 0);
            $goods_model = new GoodsModel();
            $res         = $goods_model->modifyGoodsState($goods_ids, $goods_state, $this->site_id);
            return $res;
        }
    }

    /**
     * 编辑商品库存
     * @return multitype:number unknown
     */
    public function editGoodsStock()
    {
        if (request()->isAjax()) {
            $sku_list = input("sku_list", '');
            $model    = new GoodsModel;
            $res      = $model->editGoodsStock($sku_list);
            return $res;
        }
    }

    /**
     * 获取商品分类列表
     * @return \multitype
     */
    public function getCategoryList()
    {
        if (request()->isAjax()) {
            $category_id          = input("category_id", 0);
            $goods_category_model = new GoodsCategoryModel();
            $condition            = [
                ['pid', '=', $category_id],
                ['site_id', '=', $this->site_id]
            ];

            $goods_category_list = $goods_category_model->getCategoryList($condition, 'category_id,category_name,level,commission_rate');
            return $goods_category_list;
        }
    }

    /**
     * 获取商品属性列表
     * @return \multitype
     */
    public function getAttributeList()
    {

        if (request()->isAjax()) {
            $goods_attr_model = new GoodsAttributeModel();
            $attr_class_id    = input('attr_class_id', 0);// 商品类型id
            $attribute_list   = $goods_attr_model->getAttributeList([['attr_class_id', '=', $attr_class_id], ['is_spec', '=', 0], ['site_id', '=', $this->site_id]], 'attr_id,attr_name,attr_class_id,attr_class_name,attr_type,attr_value_format');
            if (!empty($attribute_list['data'])) {
                foreach ($attribute_list['data'] as $k => $v) {
                    if (!empty($v['attr_value_format'])) {
                        $attribute_list['data'][$k]['attr_value_format'] = json_decode($v['attr_value_format'], true);
                    }
                }
            }

            return $attribute_list;
        }
    }

    /**
     * 获取SKU商品列表
     * @return \multitype
     */
    public function getGoodsSkuList()
    {
        if (request()->isAjax()) {
            $goods_id    = input("goods_id", 0);
            $goods_model = new GoodsModel();
            $res         = $goods_model->getGoodsSkuList([['goods_id', '=', $goods_id], ['site_id', '=', $this->site_id]], 'sku_id,sku_name,price,market_price,cost_price,stock,weight,volume,sku_no,sale_num,sku_image,spec_name,goods_id,daizhuang');
            return $res;
        }
    }

    public function getGoodsStocklogList()
    {
        if (request()->isAjax()) {
            $goods_id    = input("goods_id", 0);
            $goods_model = new GoodsModel();
            $res         = $goods_model->getGoodsStocklogList([['goods_id', '=', $goods_id]]);
            if (!empty($res['data']['list'])) {
                $type_names=[
                    1=>'订单扣减',
                    2=>'订单返回',
                    3=>'人工入库',
                    4=>'人工报损',
                    5=>'人工调配'
                ];
                foreach ($res['data']['list'] as $k => $v) {
                    $res['data']['list'][$k]['type_name']=$type_names[$v['type']];
                }
            }
            return $res;
        }
    }

    /**
     * 商品选择组件
     * @return \multitype
     */
    public function goodsSelect()
    {
        if (request()->isAjax()) {
            $page           = input('page', 1);
            $page_size      = input('page_size', PAGE_LIST_ROWS);
            $goods_name     = input('goods_name', '');
            $goods_id       = input('goods_id', 0);
            $is_virtual     = input('is_virtual', '');// 是否虚拟类商品（0实物1.虚拟）
            $min_price      = input('min_price', 0);
            $max_price      = input('max_price', 0);
            $goods_class    = input('goods_class', "");// 商品类型，实物、虚拟
            $category_id    = input('category_id', "");// 商品分类id
            $promotion      = input('promotion', '');//营销活动标识：pintuan、groupbuy、fenxiao、bargain
            $promotion_type = input('promotion_type', "");
            $label_id       = input('label_id', "");

            if (!empty($promotion) && addon_is_exit($promotion)) {
                $pintuan_name = input('pintuan_name', '');//拼团活动
                $goods_list   = event('GoodsListPromotion', ['page' => $page, 'page_size' => $page_size, 'site_id' => $this->site_id, 'promotion' => $promotion, 'pintuan_name' => $pintuan_name, 'goods_name' => $goods_name], true);
            } else {
                $condition = [
                    ['is_delete', '=', 0],
                    ['goods_state', '=', 1],
                    ['site_id', '=', $this->site_id]
                ];
                if (!empty($goods_name)) {
                    $condition[] = ['goods_name|goods_chi|keywords|category_names', 'like', '%' . $goods_name . '%'];
                }
                if ($is_virtual !== "") {
                    $condition[] = ['is_virtual', '=', $is_virtual];
                }
                if ($goods_id) {
                    //$goods_ids = explode(',',$goods_id);
                    $condition[] = ['goods_id', 'in', $goods_id];
                }
                if (!empty($category_id)) {
                    $condition[] = ['category_id', 'like', '%,' . $category_id . '%'];
                }

                if (!empty($promotion_type)) {
                    $condition[] = ['promotion_addon', 'like', "%{$promotion_type}%"];
                }

                if (!empty($label_id)) {
                    $condition[] = ['label_id', '=', $label_id];
                }

                if ($goods_class !== "") {
                    $condition[] = ['goods_class', '=', $goods_class];
                }

                if ($min_price != "" && $max_price != "") {
                    $condition[] = ['price', 'between', [$min_price, $max_price]];
                } elseif ($min_price != "") {
                    $condition[] = ['price', '<=', $min_price];
                } elseif ($max_price != "") {
                    $condition[] = ['price', '>=', $max_price];
                }

                $condition[] = ['goods_stock', '>', 0];

                $order       = 'create_time desc';
                $goods_model = new GoodsModel();
                $field       = 'introd,introduction,category_json,tip,goods_id,goods_name,goods_chi,goods_class_name,goods_image,price,goods_stock,create_time,is_virtual';
                $goods_list  = $goods_model->getGoodsPageList($condition, $page, $page_size, $order, $field);

                if (!empty($goods_list['data']['list'])) {
                    foreach ($goods_list['data']['list'] as $k => $v) {
                        $goods_sku_list                             = $goods_model->getGoodsSkuList([['goods_id', '=', $v['goods_id']], ['site_id', '=', $this->site_id]], 'sku_id,sku_no,sku_name,price,stock,sku_image,goods_id,goods_class_name', 'price asc');
                        $goods_sku_list                             = $goods_sku_list['data'];
                        //获取分类名称
                        $category_json  = json_decode($v['category_json']);
                        $goods_category = '';
                        foreach ($category_json as $kk => $vo) {
                            if (!empty($vo)) {
                                $category_name      = model('goods_category')->getColumn([['category_id', 'in', $vo]], 'category_name');
                                $category_name      = implode('/', $category_name);
                                $goods_category     = $category_name. '  ' .$goods_category;
                            }
                        }
                        foreach ($goods_sku_list as $kk =>$vv){
                            $goods_sku_list[$kk]['sku_name'] = $goods_sku_list[$kk]['sku_name'].$goods_list['data']['list'][$k]['goods_chi'];
                            $goods_sku_list[$kk]['tip'] = $goods_list['data']['list'][$k]['tip'];
                            if ($goods_sku_list[$kk]['tip']){
                                $goods_sku_list[$kk]['sku_name'] = $goods_sku_list[$kk]['sku_name'].'<span style="color: red">('.$goods_sku_list[$kk]['tip'].')</span>';
                            }
                            $goods_sku_list[$kk]['sku_name'] = $goods_category.'- '.$goods_sku_list[$kk]['sku_name'];
                        }
                        $goods_list['data']['list'][$k]['sku_list'] = $goods_sku_list;
                        $goods_list['data']['list'][$k]['goods_name'] = $goods_list['data']['list'][$k]['goods_name'].'/'.$goods_list['data']['list'][$k]['goods_chi'];
                    }

                }
            }
            return $goods_list;
        } else {

            //已经选择的商品sku数据
            $select_id  = input('select_id', '');
            $mode       = input('mode', 'spu');
            $max_num    = input('max_num', 0);
            $min_num    = input('min_num', 0);
            $is_virtual = input('is_virtual', '');
            $disabled   = input('disabled', 0);
            $promotion  = input('promotion', '');//营销活动标识：pintuan、groupbuy、seckill、fenxiao

            $this->assign('select_id', $select_id);
            $this->assign('mode', $mode);
            $this->assign('max_num', $max_num);
            $this->assign('min_num', $min_num);
            $this->assign('select_id', $select_id);
            $this->assign('is_virtual', $is_virtual);
            $this->assign('disabled', $disabled);
            $this->assign('promotion', $promotion);

            // 营销活动
            $goods_promotion_type = event('GoodsPromotionType');
            $this->assign('promotion_type', $goods_promotion_type);

            // 商品分组
            $goods_label_model = new GoodsLabelModel();
            $label_list        = $goods_label_model->getLabelList([['site_id', '=', $this->site_id]], 'id,label_name', 'create_time desc');
            $label_list        = $label_list['data'];
            $this->assign("label_list", $label_list);

            $goods_category_model = new GoodsCategoryModel();

            $field     = 'category_id,category_name as title';
            $condition = [
                ['pid', '=', 0],
                ['level', '=', 1],
                ['site_id', '=', $this->site_id]
            ];
            $list      = $goods_category_list = $goods_category_model->getCategoryByParent($condition, $field);
            $list      = $list['data'];
            if (!empty($list)) {
                foreach ($list as $k => $v) {
                    $two_list = $goods_category_list = $goods_category_model->getCategoryByParent(
                        [
                            ['pid', '=', $v['category_id']],
                            ['level', '=', 2],
                            ['site_id', '=', $this->site_id]
                        ],
                        $field
                    );

                    $two_list = $two_list['data'];
                    if (!empty($two_list)) {

                        foreach ($two_list as $two_k => $two_v) {
                            $three_list                   = $goods_category_list = $goods_category_model->getCategoryByParent(
                                [
                                    ['pid', '=', $two_v['category_id']],
                                    ['level', '=', 3],
                                    ['site_id', '=', $this->site_id]
                                ],
                                $field
                            );

                            $three_list = $three_list['data'];
                            if (!empty($three_list)) {

                                foreach ($three_list as $three_k => $three_v) {
                                    $four_list                   = $goods_category_list = $goods_category_model->getCategoryByParent(
                                        [
                                            ['pid', '=', $three_v['category_id']],
                                            ['level', '=', 4],
                                            ['site_id', '=', $this->site_id]
                                        ],
                                        $field
                                    );
                                    $three_list[$three_k]['children'] = $four_list['data'];
                                }
                            }
                            $two_list[$two_k]['children'] = $three_list;
                        }
                    }

                    $list[$k]['children'] = $two_list;
                }
            }

            $this->assign("category_list", $list);
            return $this->fetch("goods/goods_select");
        }
    }

    /**
     * 商品库存历史
     */
    public function goodsstock()
    {
        $goods_evaluate = new GoodsEvaluateModel();
        $goods_id = input('goods_id', 0);

        if (request()->isAjax()) {
            $page_index   = input('page', 1);
            $page_size    = input('page_size', PAGE_LIST_ROWS);
            $explain_type = input('explain_type', ''); //1好评2中评3差评
            $search_text  = input('search_keys', ''); //搜索值
            $start_time   = input('start_time', '');
            $end_time     = input('end_time', '');
            $goods_id     = input('goods_id', 0);
            $condition    = [
            ];
            //评分类型
            if ($explain_type != "") {
                $condition[] = ["type", "=", $explain_type];
                $goods_id=0;
            }
            if ($search_text != "") {
                $condition[] = ['sku_name|order_no', "like", '%' . $search_text . '%'];
                $goods_id=0;
            }
            if (!empty($start_time) && empty($end_time)) {
                $condition[] = ["time", ">=", date_to_time($start_time)];
                $goods_id=0;
            } elseif (empty($start_time) && !empty($end_time)) {
                $condition[] = ["time", "<=", date_to_time($end_time)];
                $goods_id=0;
            } elseif (!empty($start_time) && !empty($end_time)) {
                $condition[] = ['time', 'between', [date_to_time($start_time), date_to_time($end_time)]];
                $goods_id=0;
            }
            if ($goods_id) {
                $condition[] = ["goods_id", "=", $goods_id];
            }
            return $goods_evaluate->getgoodsstockPageList($condition, $page_index, $page_size, "id desc");
        } else {
            if ($goods_id){
                $goods_model    = new GoodsModel();
                $goods_sku_info = $goods_model->getGoodsSkuInfo([['goods_id', '=', $goods_id]], 'sku_id,goods_name,site_id,goods_content');
                $goods_sku_info = $goods_sku_info['data'];
                $this->assign("goods_name", $goods_sku_info['goods_name']);
            }else{
                $this->assign("goods_name", '');
            }
            $this->assign("goods_id", $goods_id);
            return $this->fetch("goods/goodsstock");
        }

    }

    public function exportGoodsstock(){
        $goods_evaluate = new GoodsEvaluateModel();
        $page_index   = input('page', 1);
        $page_size    = input('page_size', PAGE_LIST_ROWS);
        $explain_type = input('explain_type', ''); //1好评2中评3差评
        $search_text  = input('search_keys', ''); //搜索值
        $start_time   = input('start_time', '');
        $end_time     = input('end_time', '');
        $goods_id     = input('goods_id', 0);
        $condition    = [
        ];
        //评分类型
        if ($explain_type != "") {
            $condition[] = ["type", "=", $explain_type];
            $goods_id=0;
        }
        if ($search_text != "") {
            $condition[] = ['sku_name|order_no', "like", '%' . $search_text . '%'];
            $goods_id=0;
        }
        if (!empty($start_time) && empty($end_time)) {
            $condition[] = ["time", ">=", date_to_time($start_time)];
            $goods_id=0;
        } elseif (empty($start_time) && !empty($end_time)) {
            $condition[] = ["time", "<=", date_to_time($end_time)];
            $goods_id=0;
        } elseif (!empty($start_time) && !empty($end_time)) {
            $condition[] = ['time', 'between', [date_to_time($start_time), date_to_time($end_time)]];
            $goods_id=0;
        }
        if ($goods_id) {
            $condition[] = ["goods_id", "=", $goods_id];
        }

        $field = [
            'time'                 => '时间',
            'type'                 => '类型',
            'order_no'             => '订单编号',
            'goods_id'             => '产品编号',
            'sku_name'             => '产品名称',
            'old_stock'            => '修改前库存',
            'new_stock'            => '修改后库存',
            'remark'               => '备注'
        ];
        //接收需要展示的字段
        $ordernum       = $goods_evaluate->getgoodsstockList($condition);

        //重写导出方法
        set_time_limit(0);
        ini_set('memory_limit', '1024M');

        $columns_title =  [];        //设置好告诉浏览器要下载excel文件的headers
        $columns_key =  [];
        foreach($field as $kk=>$vv){
            $columns_title[]=$vv;
            $columns_key[$kk]='';
        }

        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="'.'产品库存纪录'.'.csv"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');        $fp = fopen('php://output', 'a');//打开output流
        mb_convert_variables('GBK', 'UTF-8', $columns_title);
        fputcsv($fp, $columns_title);//将数据格式化为CSV格式并写入到output流中
        //获取总数，分页循环处理
        $accessNum = $ordernum['data'];
        $perSize = 1000;
        $pages   = ceil($accessNum / $perSize);
        for($i = 1; $i <= $pages; $i++) {
            $order       = $goods_evaluate->getgoodsstockPageList($condition,$i,$perSize,'id desc','*');
            if(isset($order['data']['list'])){
                $order['data'] = $order['data']['list'];
            }else{
                $order['data'] = [];
            }

            if (!empty($order['data'])) {
                $types = [
                    1=>'订单扣减',
                    2=>'订单返回',
                    3=>'人工入库',
                    4=>'人工报损',
                    5=>'人工调配'
                ];
                foreach ($order['data'] as $key=>$vo){
                    $order['data'][$key]['time'] = date('Y-m-d h:i:s',$vo['time']);
                    $order['data'][$key]['type'] = $types[$vo['type']];
                }
            }

            foreach($order['data'] as $key => $value) {
                //$rowData = $value;                //获取每列数据，转换处理成需要导出的数据
                $rowData =array_intersect_key(array_replace($columns_key, $value), $columns_key);
                //需要格式转换，否则会乱码
                mb_convert_variables('GBK', 'UTF-8', $rowData);
                fputcsv($fp, $rowData);
            }            //释放变量的内存
            unset($order);            //刷新输出缓冲到浏览器
            ob_flush();            //必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
            flush();
        }
        fclose($fp);
        exit();
    }

    /***********************************************************商品评价**************************************************/

    /**
     * 商品评价
     */
    public function evaluate()
    {
        $goods_evaluate = new GoodsEvaluateModel();

        if (request()->isAjax()) {
            $page_index   = input('page', 1);
            $page_size    = input('page_size', PAGE_LIST_ROWS);
            $explain_type = input('explain_type', ''); //1好评2中评3差评
            $is_show      = input('is_show', ''); //1显示 0隐藏
            $search_text  = input('search_text', ''); //搜索值
            $search_type  = input('search_type', ''); //搜索类型
            $start_time   = input('start_time', '');
            $end_time     = input('end_time', '');
            $condition    = [
                ["site_id", "=", $this->site_id]
            ];
            //评分类型
            if ($explain_type != "") {
                $condition[] = ["explain_type", "=", $explain_type];
            }
            if ($is_show != "") {
                $condition[] = ["is_show", "=", $is_show];
            }
            if ($search_text != "") {
                $condition[] = [$search_type, "like", '%' . $search_text . '%'];
            }
            if (!empty($start_time) && empty($end_time)) {
                $condition[] = ["create_time", ">=", date_to_time($start_time)];
            } elseif (empty($start_time) && !empty($end_time)) {
                $condition[] = ["create_time", "<=", date_to_time($end_time)];
            } elseif (!empty($start_time) && !empty($end_time)) {
                $condition[] = ['create_time', 'between', [date_to_time($start_time), date_to_time($end_time)]];
            }
            return $goods_evaluate->getEvaluatePageList($condition, $page_index, $page_size, "create_time desc");
        } else {
            return $this->fetch("goods/evaluate");
        }

    }

    /**
     * 商品评价删除
     */
    public function deleteEvaluate()
    {

        if (request()->isAjax()) {
            $goods_evaluate = new GoodsEvaluateModel();
            $evaluate_id    = input("evaluate_id", 0);
            return $goods_evaluate->deleteEvaluate($evaluate_id);
        }
    }

    /**
     * 商品推广
     * return
     */
    public function goodsUrl()
    {
        $goods_id       = input('goods_id', '');
        $goods_model    = new GoodsModel();
        $goods_sku_info = $goods_model->getGoodsSkuInfo([['goods_id', '=', $goods_id]], 'sku_id,goods_name,site_id');
        $goods_sku_info = $goods_sku_info['data'];
        $res            = $goods_model->qrcode($goods_sku_info['sku_id'], $goods_sku_info['goods_name'], $goods_sku_info['site_id']);
        return $res;
    }

    /**
     * 商品预览
     * return
     */
    public function goodsPreview()
    {
        $goods_id       = input('goods_id', '');
        $goods_model    = new GoodsModel();
        $goods_sku_info = $goods_model->getGoodsSkuInfo([['goods_id', '=', $goods_id]], 'sku_id,goods_name,site_id,goods_content');
        $goods_sku_info = $goods_sku_info['data'];
        $res            = $goods_model->qrcode($goods_sku_info['sku_id'], str_replace("<img","<img width='100%' ",$goods_sku_info['goods_content']), $goods_sku_info['site_id']);
        return $res;
    }

    /**
     * 商品评价回复
     */
    public function evaluateApply()
    {
        if (request()->isAjax()) {
            $goods_evaluate   = new GoodsEvaluateModel();
            $evaluate_id      = input("evaluate_id", 0);
            $explain          = input("explain", 0);
            $is_first_explain = input("is_first_explain", 0);// 是否第一次回复
            $data             = [
                'evaluate_id' => $evaluate_id
            ];
            if ($is_first_explain == 0) {
                $data['explain_first'] = $explain;
            } elseif ($is_first_explain == 1) {
                $data['again_explain'] = $explain;
            }

            return $goods_evaluate->evaluateApply($data);
        }
    }

    /**
     * 商品评价回复
     */
    public function deleteContent()
    {
        if (request()->isAjax()) {
            $goods_evaluate   = new GoodsEvaluateModel();
            $evaluate_id      = input("evaluate_id", 0);
            $is_first_explain = input("is_first", 0);// 0 第一次回复，1 追评回复
            $data             = [];
            if ($is_first_explain == 0) {
                $data['explain_first'] = '';
            } elseif ($is_first_explain == 1) {
                $data['again_explain'] = '';
            }
            $condition = [
                ['evaluate_id', '=', $evaluate_id],
                ['site_id', '=', $this->site_id],
            ];

            return $goods_evaluate->editEvaluate($data, $condition);
        }
    }

    /**
     * 商品批量设置
     */
    public function batchSet()
    {
        if (request()->isAjax()) {
            $type        = input("type", '');
            $goods_ids   = input("goods_ids", '');
            $field       = input("field", '');
            $data        = !empty($field) ? json_decode($field, true) : [];
            $goods_model = new GoodsModel();

            $result = error(-1, '操作失败');
            try {
                if (!empty($goods_ids)) {
                    switch ($type) {
                        case 'group':
                            $result = $goods_model->modifyGoodsLabel($data['group'], $this->site_id, $goods_ids);
                            break;
                        case 'service':
                            $result = $goods_model->modifyGoodsService($data['server_ids'], $this->site_id, $goods_ids);
                            break;
                        case 'sale':
                            $result = $goods_model->modifyGoodsVirtualSale($data['sale'], $this->site_id, $goods_ids);
                            break;
                        case 'purchase_limit':
                            $result = $goods_model->modifyGoodsPurchaseLimit($data['max_buy'], $this->site_id, $goods_ids);
                            break;
                        case 'shipping':
                            $result = $goods_model->modifyGoodsShippingTemplate($data['is_free_shipping'], $data['shipping_template'], $this->site_id, $goods_ids);
                            break;
                    }
                }
            } catch (\Exception $e) {
                $result = error(-1, $e->getMessage());
            }
            return $result;
        }
    }

}