<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>今日生产任务</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="__STATIC__/ext/layui/css/layui.css">
    <link rel="stylesheet" href="__STATIC__/shop/css/pharmacy.css">
    <style>
        .stockin-container { min-height: 100vh; background-color: #f5f5f5; padding: 20px; }
        .stockin-header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }
        .header-buttons { display: flex; gap: 12px; align-items: center; }
        .stockin-title { font-size: 24px; color: #333; margin: 0; }
        .back-btn { background: #1E9FFF; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-flex; align-items: center; gap: 5px; }
        .back-btn:hover { background: #0078D4; color: white; text-decoration: none; }
        .stockin-main { display: flex; gap: 20px; height: calc(100vh - 140px); }
        .stockin-operation { flex: 1; background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: flex; flex-direction: column; min-height: 0; }
        .stockin-records { width: 400px; background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .operation-form { flex-shrink: 0; margin-bottom: 15px; }
        .form-group { margin-bottom: 12px; }
        .form-group label { display: block; margin-bottom: 6px; font-weight: bold; color: #333; font-size: 13px; }
        .quantity-input { width: 160px; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px; }
        .stockin-btn { background: #1E9FFF; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 13px; cursor: pointer; transition: background-color 0.3s; }
        .stockin-btn:hover:not(:disabled) { background: #0078D4; }
        .operation-tips { background: #f0f9ff; border: 1px solid #b3e5fc; border-radius: 6px; padding: 12px; display: flex; align-items: center; gap: 8px; color: #0277bd; flex-shrink: 0; }
        .batch-section { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; margin-top: 15px; flex: 1; display: flex; flex-direction: column; min-height: 0; overflow: hidden; }
        .batch-section h4 { margin: 0 0 12px 0; color: #333; flex-shrink: 0; }
        .batch-list { flex: 1; overflow-y: auto; min-height: 0; height: 100%; }
        .batch-item { display: flex; justify-content: space-between; align-items: center; padding: 8px; background: white; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 8px; }
        .batch-item .info { flex: 1; min-width: 0; }
        .batch-item .info .title { font-size: 14px; color: #333; font-weight: 600; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
        .batch-item .info .sub { font-size: 12px; color: #666; margin-top: 2px; }
        .detail-btn { background: #1E9FFF; color: white; border: none; padding: 6px 10px; border-radius: 4px; cursor: pointer; font-size: 12px; }
        .detail-btn:hover { background: #0d7dd4; }
        .batch-summary { display: flex; justify-content: space-between; align-items: center; margin-top: 12px; padding-top: 12px; border-top: 2px solid #ddd; }
        .stockin-summary { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 12px; margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center; }
        .summary-item { text-align: center; flex: 1; }
        .summary-item .label { font-size: 12px; color: #666; margin-bottom: 3px; }
        .summary-item .value { font-size: 18px; font-weight: bold; color: #1E9FFF; }
        .summary-item .unit { font-size: 12px; color: #999; margin-left: 2px; }
        .empty-notice { text-align: center; padding: 40px 20px; color: #999; }
        .empty-notice i { font-size: 48px; color: #ddd; margin-bottom: 10px; }
        .empty-notice p { margin: 5px 0; font-size: 14px; }
        .empty-notice .tip { font-size: 12px; color: #bbb; }
        /* 布局修复：确保父容器正确包裹订单列表 */
        .stockin-operation { overflow: hidden; min-height: 0; }
        .operation-form { display: flex; flex-direction: column; min-height: 0; gap: 5px; height: 100%;}
        .batch-section { min-height: 0; }
        .batch-list { flex: 1; min-height: 0; overflow-y: auto; }
        
        /* 时间切换按钮样式 */
        .time-btn {
            padding: 4px 12px;
            border: 1px solid #d9d9d9;
            background: #fff;
            color: #666;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .time-btn:hover {
            border-color: #1E9FFF;
            color: #1E9FFF;
        }
        
        .time-btn.active {
            background: #1E9FFF;
            border-color: #1E9FFF;
            color: #fff;
        }
    </style>
</head>
<body>
<div class="stockin-container">
    <div class="stockin-header">
        <h1 class="stockin-title">今日生产任务</h1>
        <div class="header-buttons">
            <button class="back-btn" id="refreshBtn">
                <i class="layui-icon layui-icon-refresh"></i>
                刷新
            </button>
            <a href="{:url('shop/Production/index')}" class="back-btn">
                <i class="layui-icon layui-icon-return"></i>
                返回生产端
            </a>
        </div>
    </div>

    <div class="stockin-main">
        <!-- 左侧：输入与订单列表 -->
        <div class="stockin-operation">
            <div class="operation-form">
                <!-- 合并今日总量和订单搜索到同一行 -->
                <div class="form-group">
                    <div style="display:flex; align-items:center; gap:20px; flex-wrap:wrap;">
                        <!-- 筛选订单区域 -->
                        <div style="display:flex; align-items:center; gap:8px;">
                            <label style="margin-bottom:0; white-space:nowrap;">筛选订单：</label>
                            <input type="number" id="assignCount" class="quantity-input" placeholder="输入数量" min="1" max="100" style="width:100px;">
                            <button class="stockin-btn" id="assignOrdersBtn">
                                <i class="layui-icon layui-icon-list"></i>
                                筛选订单
                            </button>
                        </div>
                        
                        <!-- 订单搜索区域 -->
                        <div style="display:flex; align-items:center; gap:8px; flex-wrap:wrap;">
                            <label style="margin-bottom:0; white-space:nowrap;">订单搜索：</label>
                            <input type="text" id="searchOrderNo" class="quantity-input" placeholder="输入订单号搜索" style="width:160px;">
                            <button class="stockin-btn" id="searchByOrderNoBtn">
                                <i class="layui-icon layui-icon-search"></i>
                                搜索
                            </button>
                            <button class="stockin-btn" id="searchTop20Btn" style="background:#FF5722;">
                                <i class="layui-icon layui-icon-list"></i>
                                前20条
                            </button>
                            <button class="stockin-btn" id="searchAllBtn" style="background:#FF9800;">
                                <i class="layui-icon layui-icon-template"></i>
                                全部
                            </button>
                            <button class="stockin-btn" id="selectAllBtn" style="background:#2196F3; display:none;">
                                <i class="layui-icon layui-icon-ok-circle"></i>
                                全选
                            </button>
                            <button class="stockin-btn" id="unselectAllBtn" style="background:#9E9E9E; display:none;">
                                <i class="layui-icon layui-icon-close-fill"></i>
                                取消全选
                            </button>
                            <button class="stockin-btn" id="batchProductionBtn" style="background:#4CAF50; display:none;">
                                <i class="layui-icon layui-icon-ok"></i>
                                批量生产
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="operation-tips">
                    <i class="layui-icon layui-icon-tips"></i>
                    <span>输入数量筛选待生产订单，选择订单后点击"批量生产"进行分配；或使用搜索功能查找特定订单</span>
                </div>
                <div class="batch-section">
                    <h4>今日生产订单 <span id="searchStatus" style="color:#999; font-size:12px; font-weight:normal;"></span></h4>
                    <div class="batch-list" id="orderList">
                        <div class="empty-notice">
                            <i class="layui-icon layui-icon-survey"></i>
                            <p>暂无数据</p>
                        </div>
                    </div>
                    <div class="batch-summary">
                        <div>共计：<span id="totalOrders">0</span> 单</div>
                        <div id="searchActions" style="display:none;">
                            <button class="stockin-btn" id="selectAllBtn" style="font-size:12px; padding:4px 8px;">全选</button>
                            <button class="stockin-btn" id="unselectAllBtn" style="font-size:12px; padding:4px 8px; background:#999;">取消全选</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：统计 -->
        <div class="stockin-records">
            <div class="records-section">
                <!-- 时间选择器 -->
                <div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:15px;">
                    <h3 style="margin:0;">生产统计</h3>
                    <div style="display:flex; gap:5px;">
                        <button class="time-btn active" data-period="today">今日</button>
                        <button class="time-btn" data-period="yesterday">昨日</button>
                        <button class="time-btn" data-period="week">7天</button>
                    </div>
                </div>
                
                <!-- 统计数据 -->
                <div class="stockin-summary">
                    <div class="summary-item">
                        <span class="label">已分配</span>
                        <span class="value" id="statOrderCount">0</span>
                        <span class="unit">单</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">任务目标</span>
                        <span class="value" id="statTarget">0</span>
                        <span class="unit">单</span>
                    </div>
                </div>
                
                <!-- 生产进度（常驻显示） -->
                <div id="productionProgress" style="background:#f8f9fa; border:1px solid #e9ecef; border-radius:6px; padding:12px; margin-bottom:15px;">
                    <h4 style="margin:0 0 8px 0; color:#333; font-size:14px;">📊 <span id="progressTitle">今日</span>生产进度</h4>
                    <div style="background:#fff; border-radius:4px; padding:8px; margin-bottom:8px;">
                        <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:4px;">
                            <span style="font-size:12px; color:#666;">完成进度</span>
                            <span id="progressPercent" style="font-size:12px; font-weight:bold; color:#1E9FFF;">0%</span>
                        </div>
                        <div style="background:#e9ecef; height:6px; border-radius:3px; overflow:hidden;">
                            <div id="progressBar" style="background:#1E9FFF; height:100%; width:0%; transition:width 0.3s;"></div>
                        </div>
                    </div>
                    <div style="font-size:12px; color:#666; text-align:center;">
                        已分配 <span id="assignedCount">0</span> / 目标 <span id="targetCount">0</span> 单
                    </div>
                </div>
                
                <!-- 分配订单列表（常驻显示） -->
                <div id="assignedOrders" style="background:#f8f9fa; border:1px solid #e9ecef; border-radius:6px; padding:12px; margin-bottom:15px;">
                    <h4 style="margin:0 0 8px 0; color:#333; font-size:14px;">📋 <span id="ordersTitle">今日</span>分配订单</h4>
                    <div id="assignedOrdersList" style="max-height:200px; overflow-y:auto;">
                        <div style="text-align:center; color:#999; padding:20px;">
                            暂无数据
                        </div>
                    </div>
                </div>
                
                <div class="empty-notice" id="statEmpty">
                    <i class="layui-icon layui-icon-form"></i>
                    <p>等待加载订单</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="__STATIC__/js/jquery-3.1.1.js"></script>
<script src="__STATIC__/ext/layui/layui.js"></script>
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;

    var orders = [];
    var totalResolved = 0;

    function formatTime(ts){
        if (!ts) return '';
        if (/^\d+$/.test(ts)) { // timestamp
            var d = new Date(parseInt(ts, 10) * 1000);
            return d.getFullYear() + '-' + String(d.getMonth()+1).padStart(2,'0') + '-' + String(d.getDate()).padStart(2,'0') + ' ' + String(d.getHours()).padStart(2,'0') + ':' + String(d.getMinutes()).padStart(2,'0');
        }
        return ts;
    }

    function todayStr(){
        var d = new Date();
        return d.getFullYear() + '-' + String(d.getMonth()+1).padStart(2,'0') + '-' + String(d.getDate()).padStart(2,'0');
    }

    function parseOrderRows(res){
        if (!res) return [];
        if (res.data && res.data.order) return [res.data.order];
        if (res.data && Array.isArray(res.data)) return res.data;
        if (Array.isArray(res)) return res;
        if (res.data && res.data.list && Array.isArray(res.data.list)) return res.data.list;
        if (res.list && Array.isArray(res.list)) return res.list;
        return [];
    }

    function assignOrders(){
        var count = parseInt($('#assignCount').val(), 10) || 1;
        
        if (count <= 0 || count > 100) {
            layer.msg('请输入有效的订单数量（1-100）');
            return;
        }

        var payload = { count: count };

        var loading = layer.load(2);
        $.ajax({
            url: '{:url("shop/PharmacyManagement/assignOrders")}',
            type: 'POST',
            data: payload,
            success: function(res){
                layer.close(loading);
                if (res.code === 0) {
                    if (res.data && res.data.orders && res.data.orders.length > 0) {
                        // 切换到搜索模式，显示筛选出的订单
                        isSearchMode = true;
                        orders = res.data.orders;
                        renderOrderList(true);
                        updateStats();
                        
                        // 显示批量操作按钮
                        $('#selectAllBtn').show();
                        $('#unselectAllBtn').show();
                        $('#batchProductionBtn').show();
                        layer.msg(res.message || '筛选成功');
                    } else {
                        layer.msg(res.message || '没有找到符合条件的订单');
                    }
                } else {
                    layer.msg(res.message || '筛选失败');
                }
            },
            error: function(){
                layer.close(loading);
                layer.msg('请求失败，请重试');
            }
        });
    }



    function updateStats(){
        $('#statOrderCount').text(orders.length);
        var tot = totalResolved || parseInt($('#totalToday').val(),10) || 0;
        $('#statTarget').text(tot);
        $('#statEmpty').toggle(orders.length === 0);
    }

    // 搜索订单功能
    function searchOrders(type, params) {
        var loading = layer.load(2);
        var searchData = {
            search_type: type
        };
        
        if (type === 'order_no' && params.order_no) {
            searchData.order_no = params.order_no;
        } else if (type === 'top20') {
            searchData.limit = 20;
        } else if (type === 'all') {
            searchData.limit = 0; // 0表示不限制
        }
        
        $.ajax({
            url: '{:url("shop/PharmacyManagement/searchProductionOrders")}',
            type: 'POST',
            data: searchData,
            success: function(res){
                layer.close(loading);
                if (res.code === 0) {
                    orders = parseOrderRows(res);
                    renderOrderList(true); // 传入true表示搜索模式
                    updateStats();
                    updateSearchStatus(type, orders.length, params);
                    // 显示批量操作按钮
                    $('#selectAllBtn').toggle(orders.length > 0);
                    $('#unselectAllBtn').toggle(orders.length > 0);
                    $('#batchProductionBtn').toggle(orders.length > 0);
                } else {
                    layer.msg(res.message || '搜索失败');
                }
            },
            error: function(){
                layer.close(loading);
                layer.msg('搜索请求失败，请重试');
            }
        });
    }
    
    // 更新搜索状态显示
    function updateSearchStatus(type, count, params) {
        var status = '';
        if (type === 'order_no') {
            status = '(搜索: ' + (params.order_no || '') + ')';
        } else if (type === 'top20') {
            status = '(前20条)';
        } else if (type === 'all') {
            status = '(全部)';
        }
        $('#searchStatus').text(status);
    }
    
    // 修改renderOrderList函数以支持搜索模式
    function renderOrderList(isSearchMode){
        var $list = $('#orderList');
        if (!orders.length){
            $list.html('<div class="empty-notice"><i class="layui-icon layui-icon-survey"></i><p>暂无数据</p></div>');
            $('#totalOrders').text(0);
            return;
        }
        var html = '';
        orders.forEach(function(o, index){
            var orderId = o.order_id || o.id || '';
            var orderNo = o.order_no || orderId;
            var customerName = o.name || o.member_name || '-';
            var seq = (o.production_seq || 0);
            var tot = (o.production_total || totalResolved || 0);
            var sub = '序号/总量：' + (seq>0?seq:'-') + '/' + (tot>0?tot:'-') + '　时间：' + (formatTime(o.create_time_format || o.create_time));

            var checkboxHtml = '';
            if (isSearchMode) {
                checkboxHtml = '<input type="checkbox" class="order-checkbox" data-index="' + index + '" style="margin-right:8px;">';
            }
            html += '<div class="batch-item">'
                 +    checkboxHtml
                 +    '<div class="info">'
                 +        '<div class="title">'
                 +            '<span style="color:#333; font-weight:600;">订单号：' + orderNo + '</span>'
                 +            '<span style="color:#1E9FFF; font-weight:600; margin-left:15px;">客户：' + customerName + '</span>'
                 +        '</div>'
                 +        '<div class="sub">' + sub + '</div>'
                 +    '</div>'
                 +  '</div>';
        });
        $list.html(html);
        $('#totalOrders').text(orders.length);

        // 绑定事件
        $('#orderList .detail-btn').off('click').on('click', function(){
            var id = $(this).data('id');
            if (!id) { layer.msg('订单ID缺失'); return; }
            var url = '{:url("shop/Order/printOrder")}' + '?order_id=' + encodeURIComponent(id);
            window.open(url, '_blank');
        });
    }
    
    // 批量生产选中的订单
    function batchProduction() {
        var selectedOrders = [];
        $('#orderList .order-checkbox:checked').each(function(){
            var index = $(this).data('index');
            if (orders[index]) {
                selectedOrders.push(orders[index]);
            }
        });
        
        if (selectedOrders.length === 0) {
            layer.msg('请至少选择一个订单');
            return;
        }
        
        layer.confirm('确定要为选中的 ' + selectedOrders.length + ' 个订单分配生产序号吗？', function(index){
            layer.close(index);
            
            var loading = layer.load(2);
            var orderIds = selectedOrders.map(function(o){ return o.order_id || o.id; });
            var inputTotal = selectedOrders.length;
            
            $.ajax({
                url: '{:url("shop/PharmacyManagement/batchAssignProductionOrders")}',
                type: 'POST',
                data: {
                    order_ids: orderIds,
                    total: inputTotal
                },
                success: function(res){
                    layer.close(loading);
                    if (res.code === 0) {
                        layer.msg('批量分配成功');

                        // 更新右侧栏显示已确认信息
                        updateBatchProductionStats(res.data, selectedOrders.length);

                        // 从左侧订单列表中移除已分配的订单
                        var selectedIndexes = [];
                        $('#orderList .order-checkbox:checked').each(function(){
                            selectedIndexes.push($(this).data('index'));
                        });

                        // 从orders数组中移除已分配的订单（从大到小排序，避免索引混乱）
                        selectedIndexes.sort(function(a, b) { return b - a; });
                        selectedIndexes.forEach(function(index) {
                            if (orders[index]) {
                                orders.splice(index, 1);
                            }
                        });

                        // 重新渲染订单列表
                        renderOrderList(true);
                        updateStats();

                        // 如果没有订单了，隐藏批量操作按钮
                        if (orders.length === 0) {
                            $('#selectAllBtn').hide();
                            $('#unselectAllBtn').hide();
                            $('#batchProductionBtn').hide();
                        }
                    } else if (res.code === 1 && res.data && res.data.insufficient_items) {
                        // 散药库存不足，显示拆零提示
                        showStockInsufficientDialog(res.data.insufficient_items, selectedOrders, orderIds, inputTotal);
                    } else {
                        layer.msg(res.message || '批量分配失败');
                    }
                },
                error: function(){
                    layer.close(loading);
                    layer.msg('请求失败，请重试');
                }
            });
        });
    }
    
    // 获取生产信息（支持不同时间段）
    function loadProductionInfo(period) {
        period = period || 'today';
        $.ajax({
            url: '{:url("shop/PharmacyManagement/getProductionInfo")}',
            type: 'POST',
            data: { period: period },
            success: function(res){
                if (res.code === 0) {
                    updateProductionDisplay(res.data);
                }
            },
            error: function(){
                console.log('获取生产信息失败');
            }
        });
    }

    // 获取今日生产信息（保持向后兼容）
    function loadTodayProductionInfo() {
        loadProductionInfo('today');
    }
    
    // 更新生产信息显示（支持不同时间段）
    function updateProductionDisplay(data) {
        var assignedCount = data.assigned_count || 0;
        var total = data.total || 0;
        var progressPercent = data.progress_percent || 0;
        var assignedOrders = data.assigned_orders || [];
        var periodName = data.period_name || '今日';
        
        // 更新基本统计
        $('#statOrderCount').text(assignedCount);
        $('#statTarget').text(total);
        
        // 更新标题
        $('#progressTitle').text(periodName);
        $('#ordersTitle').text(periodName);
        
        // 更新进度信息
        $('#progressPercent').text(progressPercent + '%');
        $('#progressBar').css('width', progressPercent + '%');
        $('#assignedCount').text(assignedCount);
        $('#targetCount').text(total);
        
        // 更新分配订单列表
        if (assignedOrders.length > 0) {
            var ordersHtml = '';
            assignedOrders.forEach(function(order) {
                var customerName = order.name || order.member_name || '未知';
                var orderNo = order.order_no || order.order_id;
                var seq = order.production_seq || '-';
                var date = order.production_date || '';
                
                ordersHtml += '<div style="padding:6px 0; border-bottom:1px solid #eee; font-size:12px;">';
                ordersHtml += '<div style="display:flex; justify-content:space-between; align-items:center;">';
                ordersHtml += '<span style="color:#333;">订单: ' + orderNo + '</span>';
                ordersHtml += '<span style="color:#1E9FFF; font-weight:bold;">序号' + seq + '</span>';
                ordersHtml += '</div>';
                ordersHtml += '<div style="color:#666; margin-top:2px;">客户: ' + customerName;
                if (data.period === 'week') {
                    ordersHtml += ' | ' + date;
                }
                ordersHtml += '</div>';
                // 生产确认单链接
                if (order.order_id) {
                    ordersHtml += '<div style="margin-top:6px;">'
                        + '<a class="detail-btn" href="/shop/Order/printOrder.html?order_id=' + order.order_id + '" target="_blank">生产确认单</a>'
                        + '</div>';
                }
                ordersHtml += '</div>';
            });
            $('#assignedOrdersList').html(ordersHtml);
        } else {
            $('#assignedOrdersList').html('<div style="text-align:center; color:#999; padding:20px;">暂无数据</div>');
        }
    }

    // 更新今日生产信息显示（保持向后兼容）
    function updateTodayProductionDisplay(data) {
        updateProductionDisplay(data);
    }
    
    // 更新批量生产统计信息
    function updateBatchProductionStats(data, selectedCount) {
        var successCount = data.success_count || 0;
        var failedCount = (data.failed_orders && data.failed_orders.length) || 0;
        var total = data.total || 0;
        
        // 显示详细的确认信息
        var confirmHtml = '<div class="batch-confirm" style="background:#e8f5e8; border:1px solid #4CAF50; border-radius:6px; padding:12px; margin-top:10px;">';
        confirmHtml += '<h4 style="margin:0 0 8px 0; color:#2E7D32; font-size:14px;">✓ 批量生产已确认</h4>';
        confirmHtml += '<div style="font-size:12px; color:#333; line-height:1.5;">';
        confirmHtml += '<div>选择订单：<strong>' + selectedCount + '</strong> 个</div>';
        confirmHtml += '<div>成功分配：<strong style="color:#4CAF50;">' + successCount + '</strong> 个</div>';
        if (failedCount > 0) {
            confirmHtml += '<div>分配失败：<strong style="color:#F44336;">' + failedCount + '</strong> 个</div>';
        }
        confirmHtml += '<div>今日总量：<strong>' + total + '</strong> 单</div>';
        confirmHtml += '<div style="margin-top:6px; color:#666;">确认时间：' + new Date().toLocaleString() + '</div>';
        confirmHtml += '</div></div>';
        
        // 移除之前的确认信息，添加新的
        $('.records-section .batch-confirm').remove();
        $('.records-section').append(confirmHtml);
        
        // 重新加载当前时间段的生产信息
        setTimeout(function() {
            var currentPeriod = $('.time-btn.active').data('period') || 'today';
            loadProductionInfo(currentPeriod);
        }, 500);
    }

    // 时间切换按钮事件绑定
    $('.time-btn').on('click', function() {
        var period = $(this).data('period');
        
        // 更新按钮状态
        $('.time-btn').removeClass('active');
        $(this).addClass('active');
        
        // 加载对应时间段的数据
        loadProductionInfo(period);
    });

    // 事件绑定
    $('#assignOrdersBtn').on('click', assignOrders);
    
    // 搜索相关事件绑定
    $('#searchByOrderNoBtn').on('click', function(){
        var orderNo = $('#searchOrderNo').val().trim();
        if (!orderNo) {
            layer.msg('请输入订单号');
            return;
        }
        searchOrders('order_no', {order_no: orderNo});
    });
    
    $('#searchTop20Btn').on('click', function(){
        searchOrders('top20', {});
    });
    
    $('#searchAllBtn').on('click', function(){
        searchOrders('all', {});
    });
    
    $('#batchProductionBtn').on('click', batchProduction);
    
    // 全选/取消全选
    $('#selectAllBtn').on('click', function(){
        $('#orderList .order-checkbox').prop('checked', true);
    });
    
    $('#unselectAllBtn').on('click', function(){
        $('#orderList .order-checkbox').prop('checked', false);
    });
    
    // 支持回车搜索
    $('#searchOrderNo').on('keypress', function(e){
        if (e.which === 13) {
            $('#searchByOrderNoBtn').click();
        }
    });
    
    // 显示散药库存不足对话框
    function showStockInsufficientDialog(insufficientItems, selectedOrders, orderIds, inputTotal) {
        var content = '<div style="max-height:400px; overflow-y:auto;">';
        content += '<h4 style="color:#FF5722; margin-bottom:15px;">⚠️ 散药库存不足，需要拆零操作</h4>';
        content += '<div style="background:#fff3cd; border:1px solid #ffeaa7; border-radius:4px; padding:10px; margin-bottom:15px;">';
        content += '<strong>提示：</strong>请先对以下商品进行拆零操作，然后重新尝试分配生产序号。';
        content += '</div>';
        content += '<table style="width:100%; border-collapse:collapse; font-size:12px;">';
        content += '<thead><tr style="background:#f8f9fa;">';
        content += '<th style="border:1px solid #ddd; padding:8px; text-align:left;">商品名称</th>';
        content += '<th style="border:1px solid #ddd; padding:8px; text-align:center;">货架号</th>';
        content += '<th style="border:1px solid #ddd; padding:8px; text-align:center;">需要数量</th>';
        content += '<th style="border:1px solid #ddd; padding:8px; text-align:center;">当前库存</th>';
        content += '<th style="border:1px solid #ddd; padding:8px; text-align:center;">缺少数量</th>';
        content += '<th style="border:1px solid #ddd; padding:8px; text-align:center;">建议拆零</th>';
        content += '</tr></thead><tbody>';

        insufficientItems.forEach(function(item) {
            content += '<tr>';
            content += '<td style="border:1px solid #ddd; padding:8px;">' + item.sku_name + '</td>';
            content += '<td style="border:1px solid #ddd; padding:8px; text-align:center; font-weight:bold; color:#1E9FFF;">' + (item.shelf_no || '-') + '</td>';
            content += '<td style="border:1px solid #ddd; padding:8px; text-align:center;">' + item.required + '颗</td>';
            content += '<td style="border:1px solid #ddd; padding:8px; text-align:center;">' + item.available + '颗</td>';
            content += '<td style="border:1px solid #ddd; padding:8px; text-align:center; color:#FF5722; font-weight:bold;">' + item.shortage + '颗</td>';
            content += '<td style="border:1px solid #ddd; padding:8px; text-align:center; color:#4CAF50; font-weight:bold;">' + item.bottles_needed + '瓶</td>';
            content += '</tr>';
        });

        content += '</tbody></table>';
        content += '<div style="margin-top:15px; padding:10px; background:#e8f5e8; border-radius:4px; font-size:12px;">';
        content += '<strong>操作建议：</strong><br>';
        content += '1. 点击下方"拆零操作"按钮，在新窗口打开拆零页面（本窗口保持打开）<br>';
        content += '2. 在拆零页面扫描对应货架号的商品进行拆零操作<br>';
        content += '3. 拆零完成后，返回本窗口点击"重新检查"按钮<br>';
        content += '4. 或者点击"取消"关闭此窗口返回订单列表';
        content += '</div>';
        content += '</div>';

        layer.open({
            type: 1,
            title: '散药库存检查',
            area: ['800px', '600px'],
            content: content,
            btn: ['拆零操作', '重新检查', '取消'],
            yes: function(layerIndex) {
                // 不关闭弹窗，直接在新窗口打开拆零页面
                window.open('{:url("shop/PharmacyManagement/split")}', '_blank');
                return false; // 阻止默认的关闭行为
            },
            btn2: function(layerIndex) {
                layer.close(layerIndex);
                // 重新尝试分配生产序号
                retryBatchProduction(selectedOrders, orderIds, inputTotal);
            },
            btn3: function(layerIndex) {
                layer.close(layerIndex);
                return true;
            }
        });
    }

    // 重新尝试批量生产分配
    function retryBatchProduction(selectedOrders, orderIds, inputTotal) {
        var loading = layer.load(2);

        $.ajax({
            url: '{:url("shop/PharmacyManagement/batchAssignProductionOrders")}',
            type: 'POST',
            data: {
                order_ids: orderIds,
                total: inputTotal
            },
            success: function(res){
                layer.close(loading);
                if (res.code === 0) {
                    layer.msg('批量分配成功');

                    // 更新右侧栏显示已确认信息
                    updateBatchProductionStats(res.data, selectedOrders.length);

                    // 从左侧订单列表中移除已分配的订单
                    var selectedIndexes = [];
                    $('#orderList .order-checkbox:checked').each(function(){
                        selectedIndexes.push($(this).data('index'));
                    });

                    // 从orders数组中移除已分配的订单（从大到小排序，避免索引混乱）
                    selectedIndexes.sort(function(a, b) { return b - a; });
                    selectedIndexes.forEach(function(index) {
                        if (orders[index]) {
                            orders.splice(index, 1);
                        }
                    });

                    // 重新渲染订单列表
                    renderOrderList(true);
                    updateStats();

                    // 如果没有订单了，隐藏批量操作按钮
                    if (orders.length === 0) {
                        $('#selectAllBtn').hide();
                        $('#unselectAllBtn').hide();
                        $('#batchProductionBtn').hide();
                    }
                } else if (res.code === 1 && res.data && res.data.insufficient_items) {
                    // 仍然库存不足
                    showStockInsufficientDialog(res.data.insufficient_items, selectedOrders, orderIds, inputTotal);
                } else {
                    layer.msg(res.message || '批量分配失败');
                }
            },
            error: function(){
                layer.close(loading);
                layer.msg('请求失败，请重试');
            }
        });
    }

    // 页面初始化：加载今日生产信息
    loadProductionInfo('today');
});
</script>
</body>
</html>
