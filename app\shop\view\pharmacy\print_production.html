<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link href="__STATIC__/css/seller_center.css" rel="stylesheet" type="text/css">
    <style type="text/css">
        body {
            background: #FFF none;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }

        #printarea {
            color: #000;
        }

        /* 生产记录单样式 */
        .production-container {
            width: 210mm;
            margin: 0 auto;
            padding: 10mm;
            font-size: 12px;
            line-height: 1.4;
            box-sizing: border-box;
        }

        .production-container * {
            box-sizing: border-box;
        }

        @media print {
            .print-btn, .a5-size, .a5-tip, .a4-size, .a4-tip {
                display: none !important;
            }

            body {
                margin: 0 !important;
                padding: 0 !important;
                background: #FFF none !important;
                font-family: "Microsoft YaHei", Arial, sans-serif !important;
                color: #000 !important;
            }

            .production-container {
                width: 210mm !important;
                margin: 0 !important;
                padding: 10mm !important;
                font-size: 12px !important;
                line-height: 1.4 !important;
                box-sizing: border-box !important;
            }

            /* 打印时的表格样式 */
            table {
                border-collapse: collapse !important;
                width: 100% !important;
                margin-bottom: 10px !important;
            }

            table, th, td {
                border: 1px solid #000 !important;
            }

            th, td {
                padding: 8px !important;
                text-align: center !important;
                vertical-align: middle !important;
            }

            th {
                background-color: #f5f5f5 !important;
                font-weight: bold !important;
            }

            /* 打印时的标题样式 */
            .header-title {
                text-align: center !important;
                font-size: 18px !important;
                font-weight: bold !important;
                margin-bottom: 10px !important;
            }

            .header-info {
                display: flex !important;
                justify-content: space-between !important;
                margin-bottom: 15px !important;
                font-size: 14px !important;
            }

            .production-info {
                display: flex !important;
                gap: 20px !important;
                margin-bottom: 15px !important;
            }

            /* 打印时的原料统计样式 */
            .summary-section {
                display: flex !important;
                gap: 20px !important;
                margin-top: 20px !important;
            }

            .summary-left, .summary-right {
                flex: 1 !important;
            }

            .summary-left table, .summary-right table {
                height: 100% !important;
                table-layout: fixed !important;
            }

            .summary-left td, .summary-right td {
                height: 35px !important;
                vertical-align: middle !important;
            }

            .summary-left th, .summary-right th {
                height: 35px !important;
                vertical-align: middle !important;
            }

            /* 打印时的签名区域样式 */
            .signature-section {
                margin-top: 30px !important;
                display: flex !important;
                justify-content: space-between !important;
            }

            /* 确保打印区域样式正确 */
            #printarea {
                color: #000 !important;
                background: #FFF !important;
            }

            /* 打印时隐藏不必要的元素 */
            .print-layout {
                background: none !important;
                border: none !important;
                box-shadow: none !important;
                padding: 0 !important;
                margin: 0 !important;
                width: 100% !important;
                min-height: auto !important;
            }

            .print-page {
                position: static !important;
                width: 100% !important;
                min-height: auto !important;
                margin: 0 !important;
                overflow: visible !important;
            }

            /* 打印时的图片样式 */
            img {
                max-width: 40px !important;
                max-height: 40px !important;
                width: 40px !important;
                display: inline-block !important;
            }

            /* 确保flex布局在打印时正常工作 */
            .header-info, .production-info, .summary-section, .signature-section {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            /* 强制显示背景色 */
            th, .header-title {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* 确保页面不会被分页截断 */
            .production-container {
                page-break-inside: avoid !important;
            }

            table {
                page-break-inside: auto !important;
            }

            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
        }

        /* 表格样式 */
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 10px;
        }

        table, th, td {
            border: 1px solid #000;
        }

        th, td {
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }

        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .header-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .header-info {
            display: flex;
            justify-content: end;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .production-info {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }

        .time-section {
            margin-bottom: 20px;
        }

        .time-title {
            background-color: #e8e8e8;
            padding: 5px 10px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .summary-section {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            align-items: flex-start;
        }

        .summary-left, .summary-right {
            flex: 1;
        }

        /* 确保原料统计表格对齐 */
        .summary-left table, .summary-right table {
            height: auto;
            table-layout: fixed;
        }

        .summary-left tbody, .summary-right tbody {
            vertical-align: top;
        }

        .summary-left td, .summary-right td {
            height: 35px; /* 固定行高确保对齐 */
            vertical-align: middle;
        }

        .summary-left th, .summary-right th {
            height: 35px; /* 固定表头高度 */
            vertical-align: middle;
        }

        .signature-section {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }

        /* 重写打印布局样式，适配生产记录单 */
        .print-layout {
            font-size: 12px;
            background: #FAFAFA;
            border: solid 1px #CCC;
            position: relative;
            width: 210mm;
            min-height: 297mm; /* 改为最小高度，允许内容撑开 */
            padding: 5mm 50mm 5mm 5mm;
            margin: 20px auto;
            box-shadow: 2px 2px 2px rgba(204,204,204,0.5);
        }

        .print-layout .a4-size {
            background: #FFF;
            border: dashed 1px #ccc;
            width: 210mm;
            position: absolute;
            top: 5mm;
            left: 5mm;
            padding: 1px;
            height: auto; /* 改为自动高度 */
            min-height: 287mm; /* 设置最小高度匹配内容 */
            z-index: 1;
        }

        .print-layout .a4-tip {
            color: #333;
            width: 37mm;
            position: absolute;
            z-index: 2;
            right: 8mm;
            top: 250mm; /* 调整位置，避免与内容重叠 */
        }

        .print-layout .print-page {
            width: 210mm;
            min-height: 287mm; /* 匹配内容高度 */
            position: absolute;
            z-index: 3;
            top: 5mm;
            left: 5mm;
            margin: 1px;
            overflow: visible; /* 允许内容溢出显示 */
        }
    </style>
    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        window.ns_url = {
            baseUrl: "ROOT_URL/",
            route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
        };
    </script>
    <script type="text/javascript" src="__STATIC__/js/common.js" charset="utf-8"></script>
    <script type="text/javascript" src="__STATIC__/js/jquery.printarea.js" charset="utf-8"></script>
    <title>{$menu_info['title']|default="打印生产记录单"} - {$shop_info['site_name']|default=""}</title>
</head>
<body>
{notempty name="production_detail"}
<div class="print-layout">
    <div class="print-btn" id="printbtn" title="选择喷墨或激光打印机<br/>根据下列纸张描述进行<br/>设置并打印生产记录单"><i></i><a href="javascript:void(0);">打印</a></div>
    <div class="a5-size"></div>
    <dl class="a5-tip">
        <dt>
            <h1>A5</h1>
            <em>Size: 210mm x 148mm</em></dt>
        <dd>当打印设置选择A5纸张、横向打印、无边距时每张A5打印纸可输出1页订单。</dd>
    </dl>
    <div class="a4-size"></div>
    <dl class="a4-tip">
        <dt>
            <h1>A4</h1>
            <em>Size: 210mm x 297mm</em></dt>
        <dd>当打印设置选择A4纸张、竖向打印、无边距时每张A4打印纸可输出2页订单。</dd>
    </dl>
    <div class="print-page">
        <div id="printarea">
            <!-- 内嵌CSS样式，确保打印时不丢失 -->
            <style type="text/css">
                /* 基础样式 */
                body {
                    background: #FFF none !important;
                    font-family: "Microsoft YaHei", Arial, sans-serif !important;
                    color: #000 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                /* 生产记录单容器 */
                .production-container {
                    width: 210mm !important;
                    margin: 0 auto !important;
                    padding: 10mm !important;
                    font-size: 12px !important;
                    line-height: 1.4 !important;
                    box-sizing: border-box !important;
                    background: #FFF !important;
                }

                /* 表格样式 */
                table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                    margin-bottom: 10px !important;
                }

                table, th, td {
                    border: 1px solid #000 !important;
                }

                th, td {
                    padding: 8px !important;
                    text-align: center !important;
                    vertical-align: middle !important;
                }

                th {
                    background-color: #f5f5f5 !important;
                    font-weight: bold !important;
                }

                /* 标题样式 */
                .header-title {
                    text-align: center !important;
                    font-size: 18px !important;
                    font-weight: bold !important;
                    margin-bottom: 10px !important;
                }

                .header-info {
                    text-align: right !important;
                    margin-bottom: 15px !important;
                    font-size: 14px !important;
                }

                /* 生产信息表格样式 */
                .production-info-table {
                    width: 100% !important;
                    margin-bottom: 15px !important;
                    border-collapse: collapse !important;
                }

                .production-info-table td {
                    padding: 8px !important;
                    border: 1px solid #000 !important;
                    text-align: center !important;
                    vertical-align: middle !important;
                    font-size: 12px !important;
                }

                .production-info-table .label-cell {
                    background-color: #f5f5f5 !important;
                    font-weight: bold !important;
                    width: 12.5% !important;
                }

                .production-info-table .data-cell {
                    width: 12.5% !important;
                }

                .production-info-table .qr-label-cell {
                    background-color: #f5f5f5 !important;
                    font-weight: bold !important;
                    width: 12.5% !important;
                    text-orientation: mixed !important;
                }

                .production-info-table .qr-image-cell {
                    width: 12.5% !important;
                    height: 60px !important;
                }

                /* 原料统计样式 */
                .summary-section {
                    display: flex !important;
                    gap: 20px !important;
                    margin-top: 20px !important;
                    align-items: flex-start !important;
                }

                .summary-left, .summary-right {
                    flex: 1 !important;
                }

                .summary-left table, .summary-right table {
                    height: auto !important;
                    table-layout: fixed !important;
                }

                .summary-left td, .summary-right td {
                    height: 35px !important;
                    vertical-align: middle !important;
                }

                .summary-left th, .summary-right th {
                    height: 35px !important;
                    vertical-align: middle !important;
                }

                /* 签名区域样式 */
                .signature-section {
                    margin-top: 30px !important;
                    display: flex !important;
                    justify-content: space-between !important;
                }

                /* 图片样式 */
                img {
                    max-width: 40px !important;
                    max-height: 40px !important;
                    width: 40px !important;
                    display: inline-block !important;
                }

                /* 强制显示所有样式，包括背景色和边框 */
                * {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                /* 专门针对打印的媒体查询 */
                @media print {
                    body {
                        background: #FFF none !important;
                        font-family: "Microsoft YaHei", Arial, sans-serif !important;
                        color: #000 !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    .production-container {
                        width: 210mm !important;
                        margin: 0 !important;
                        padding: 10mm !important;
                        font-size: 12px !important;
                        line-height: 1.4 !important;
                        box-sizing: border-box !important;
                        background: #FFF !important;
                    }

                    table {
                        border-collapse: collapse !important;
                        width: 100% !important;
                        margin-bottom: 10px !important;
                    }

                    table, th, td {
                        border: 1px solid #000 !important;
                    }

                    th, td {
                        padding: 8px !important;
                        text-align: center !important;
                        vertical-align: middle !important;
                    }

                    th {
                        background-color: #f5f5f5 !important;
                        font-weight: bold !important;
                    }

                    .header-title {
                        text-align: center !important;
                        font-size: 18px !important;
                        font-weight: bold !important;
                        margin-bottom: 10px !important;
                    }

                    .header-info {
                        text-align: right !important;
                        margin-bottom: 15px !important;
                        font-size: 14px !important;
                    }

                    /* 生产信息表格打印样式 */
                    .production-info-table {
                        width: 100% !important;
                        margin-bottom: 15px !important;
                        border-collapse: collapse !important;
                    }

                    .production-info-table td {
                        padding: 8px !important;
                        border: 1px solid #000 !important;
                        text-align: center !important;
                        vertical-align: middle !important;
                        font-size: 12px !important;
                    }

                    .production-info-table .label-cell {
                        background-color: #f5f5f5 !important;
                        font-weight: bold !important;
                        width: 12.5% !important;
                    }

                    .production-info-table .data-cell {
                        width: 12.5% !important;
                    }

                    .production-info-table .qr-label-cell {
                        background-color: #f5f5f5 !important;
                        font-weight: bold !important;
                        width: 12.5% !important;
                        text-orientation: mixed !important;
                    }

                    .production-info-table .qr-image-cell {
                        width: 12.5% !important;
                        height: 60px !important;
                    }

                    .summary-section {
                        display: flex !important;
                        gap: 20px !important;
                        margin-top: 20px !important;
                        align-items: flex-start !important;
                    }

                    .summary-left, .summary-right {
                        flex: 1 !important;
                    }

                    .signature-section {
                        margin-top: 30px !important;
                        display: flex !important;
                        justify-content: space-between !important;
                    }

                    img {
                        max-width: 40px !important;
                        max-height: 40px !important;
                        width: 40px !important;
                        display: inline-block !important;
                    }

                    /* 确保所有元素在打印时保持样式 */
                    * {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }

                    /* 确保页面不会被分页截断 */
                    .production-container {
                        page-break-inside: avoid !important;
                    }

                    table {
                        page-break-inside: auto !important;
                    }

                    tr {
                        page-break-inside: avoid !important;
                        page-break-after: auto !important;
                    }
                }
            </style>

            <!-- 生产记录单布局 -->
            <div class="production-container">
                <!-- 标题 -->
                <div class="header-title">山有词生产记录单</div>

                <!-- 头部信息 -->
                <div class="header-info">
                    {$production_detail.create_time_format|default='-'} 生产记录编号：{$production_detail.id|default='-'}
                </div>

                <!-- 生产信息表格 -->
                <table class="production-info-table">
                    <tr>
                        <td class="label-cell">模板名称</td>
                        <td class="data-cell">{$production_detail.template_name|default=''}</td>
                        <td class="label-cell">计划套数</td>
                        <td class="data-cell">{$production_detail.production_quantity|default=''}套</td>
                    </tr>
                    <tr>
                        <td class="label-cell">实际套数</td>
                        <td class="data-cell">{if $production_detail.final_quantity > 0}{$production_detail.final_quantity}套{else}_________套{/if}</td>
                        <td class="label-cell">打印时间</td>
                        <td class="data-cell">{:date('Y-m-d H:i')}</td>
                    </tr>
                </table>

                <!-- 原料消耗表格（按时间段分组） -->
                <table>
                    <thead>
                        <tr>
                            <th width="7%">时间段</th>
                            <th width="7%">商品编码</th>
                            <th width="7%">货架号</th>
                            <th width="7%">品牌</th>
                            <th width="11%">英文名称</th>
                            <th width="11%">中文名称</th>
                            <th width="7%">图片</th>
                            <th width="7%">(粒/餐)</th>
                            <th width="12%">套餐换算</th>
                            <th width="7%">核对</th>
                        </tr>
                    </thead>
                    <tbody>
                        {php}
                        // 时间段映射（匹配数据库字段）
                        $time_mapping = [
                            'earlymoning' => '晨起包',
                            'moning' => '早餐包',
                            'canjian' => '餐间包',
                            'aftnoon' => '午餐包',
                            'night' => '晚餐包',
                            'sleep' => '睡前包'
                        ];
                        
                        // 按时间段分组原料
                        $time_groups = [];
                        if (isset($consumed_materials) && is_array($consumed_materials)) {
                            foreach ($consumed_materials as $material) {
                                // 从模板数据中获取时间段信息
                                $time_period = 'other';
                                if (isset($material['time_period'])) {
                                    $time_period = $material['time_period'];
                                } else {
                                    // 如果没有time_period字段，尝试从其他字段推断
                                    // 这里需要根据实际的数据结构来调整
                                    $time_period = 'other';
                                }
                                
                                if (!isset($time_groups[$time_period])) {
                                    $time_groups[$time_period] = [];
                                }
                                $time_groups[$time_period][] = $material;
                            }
                        }
                        
                        // 如果没有时间段信息，将所有材料归为其他类别
                        if (empty($time_groups) && isset($consumed_materials) && is_array($consumed_materials)) {
                            $time_groups['other'] = $consumed_materials;
                        }
                        
                        // 计算各时间段的消耗粒数
                        $period_consumption_counts = [];
                        foreach ($time_groups as $period => $materials) {
                            $total = 0;
                            foreach ($materials as $material) {
                                $total += intval($material['consumed_quantity'] ?? 0);
                            }
                            $period_consumption_counts[$period] = $total;
                        }
                        {/php}
                        
                        <!-- 按时间段显示原料 -->
                        {php}
                        foreach ($time_groups as $time_period => $materials) {
                            if (count($materials) > 0) {
                                $time_name = isset($time_mapping[$time_period]) ? $time_mapping[$time_period] : $time_period;
                                $rowspan_count = count($materials) + 1;
                                
                                foreach ($materials as $index => $material) {
                                    echo '<tr';
                                    if (isset($material['label_id']) && $material['label_id'] == 8) {
                                        echo ' style="background-color: #fff9c4;"';
                                    }
                                    echo '>';
                                    
                                    if ($index == 0) {
                                        echo '<td rowspan="' . $rowspan_count . '"';
                                        if (isset($material['label_id']) && $material['label_id'] == 8) {
                                            echo ' style="background-color:#fff;"';
                                        }
                                        echo '>' . $time_name . '</td>';
                                    }
                                    
                                    echo '<td>' . ($material['sku_no'] ?? '') . '</td>';
                                    echo '<td>' . ($material['shelf_no'] ?? '') . '</td>';
                                    echo '<td>' . ($material['category_name'] ?? '') . '</td>';
                                    echo '<td>' . ($material['sku_name'] ?? '') . '</td>';
                                    echo '<td>' . ($material['goods_chi'] ?? '') . '</td>';
                                    echo '<td><img src="' . img($material['sku_image'] ?? '') . '" width="40px" style="max-width:40px;max-height:40px;"></td>';
                                    
                                    $sets = (isset($production_detail['final_quantity']) && $production_detail['final_quantity'] > 0) ? $production_detail['final_quantity'] : ($production_detail['production_quantity'] ?? 1);
                                    $per_set = round(($material['consumed_quantity'] ?? 0) / max($sets, 1), 2);
                                    echo '<td>' . $per_set . '</td>';

                                    // 套餐换算列：每行显示该商品的瓶数需求
                                    $bottles_consumed = $material['bottles_consumed'] ?? 0;
                                    echo '<td>' . $bottles_consumed . '瓶</td>';

                                    echo '<td></td>';
                                    echo '</tr>';
                                }
                                
                                // 不显示时间段小计行
                            }
                        }
                        {/php}
                    </tbody>
                </table>

                <!-- 包装材料统计 -->
                <div class="summary-section">
                    <div class="summary-left">
                        <table style="margin-bottom: 0;">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>数量</th>
                                    <th>核对</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>餐盒</td>
                                    <td>{$packaging_counts.meal_box|default=0}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>封套</td>
                                    <td>{$packaging_counts.envelope|default=0}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>外盒小</td>
                                    <td>{$packaging_counts.outer_box_small|default=0}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>外盒大</td>
                                    <td>{$packaging_counts.outer_box_large|default=0}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>品牌手册</td>
                                    <td>{$packaging_counts.brand_manual|default=0}</td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="summary-right">
                        <table style="margin-bottom: 0;">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>数量</th>
                                    <th>核对</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>封口贴</td>
                                    <td>{$packaging_counts.seal_sticker|default=0}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>个性化说明书</td>
                                    <td>{$packaging_counts.personal_manual|default=0}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>快递单</td>
                                    <td>{$packaging_counts.express_bill|default=0}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 生产状态信息 -->
                <div style="margin-top: 20px;">
                    <table style="width: 100%; margin-bottom: 0;">
                        <tbody>
                            <tr>
                                <td rowspan="2" style="width: 12%; text-align: center; font-weight: bold; background-color: #f0f0f0; vertical-align: middle;">生产状态</td>
                                <td style="width: 15%; text-align: center; font-weight: bold; background-color: #f0f0f0;">状态</td>
                                <td style="width: 25%; text-align: center;">{$production_detail.status_name|default='成功'}</td>
                                <td style="width: 15%; text-align: center; font-weight: bold; background-color: #f0f0f0;">确认数量</td>
                                <td style="width: 33%; text-align: center;">{if $production_detail.final_quantity > 0}{$production_detail.final_quantity}套{else}_________套{/if}</td>
                            </tr>
                            <tr>
                                <td style="text-align: center; font-weight: bold; background-color: #f0f0f0;">备注</td>
                                <td style="text-align: center;">{$production_detail.remark|default='无'}</td>
                                <td style="text-align: center; font-weight: bold; background-color: #f0f0f0;">批次号</td>
                                <td style="text-align: center;">{$production_detail.batch_no|default=$production_detail.id}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 签名区域 -->
                <div class="signature-section">
                    <div><strong>执行人：</strong>_________________</div>
                    <div><strong>核对人：</strong>_________________</div>
                    <div><strong>确认日期：</strong>_________________</div>
                </div>
            </div>
        </div>
    </div>
</div>
        </div>
    </div>
</div>
{/notempty}
</body>
<script>
    $(function(){
        $("#printbtn").click(function(){
            $("#printarea").printArea();
        });
    });
</script>
</html>
